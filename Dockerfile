FROM maven:3.6.0-jdk-11

ENV TZ=Asia/Ho_Chi_Minh
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

WORKDIR /app

COPY pom.xml .

RUN mvn -B -e -C -T 1C org.apache.maven.plugins:maven-dependency-plugin:3.1.1:go-offline

COPY src/ ./src

COPY bin/ ./bin

RUN ["mvn", "clean", "install", "-DskipTests"]

RUN ["chmod", "+x", "bin/run.sh"]

RUN groupadd -g 1600 cdp && useradd -s /bin/bash -g 1600 -u 1600 -d /app cdp

RUN chown -R cdp:cdp /app

USER cdp

ENTRYPOINT ["sh", "bin/run.sh"]
