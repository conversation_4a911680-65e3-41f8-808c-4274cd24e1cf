# Triển khai phần API cho quản lý profile cho hệ thống CDP
A service that handles all business logic related to **user profiles**, following **Domain-Driven Design (DDD)** and **Clean Architecture** principles.

---
## Project Structure

Tham khảo: https://paste.admicro.vn/?3641f42c834e7a8f#BNPtrGuHt3M79ZbDdKrFKaLj6KpGty6Ct7zinwcaUEMW

```
└── profile
    ├── domain
    │   ├── model
    │   │   ├── Profile.java
    │   │   └── Journey.java
    │   ├── repository
    │   │   └── ProfileRepository.java
    │   └── service
    │       └── ProfileDomainService.java
    │
    ├── application
    │   ├── service
    │   │   └── ProfileApplicationService.java
    │   └── dto
    │       ├── ProfileRequest.java
    │       └── ProfileResponse.java
    │
    ├── infrastructure
    │   ├── mysql
    │   │   ├── jpa
    │   │   │   ├── ProfileJpaEntity.java
    │   │   │   ├── ProfileJpaRepository.java
    │   │   │   └── ProfileRepositoryImpl.java ← implements domain repo
    │   └── hbase
    │
    └── presentation
        └── rest
            ├── controller
            │   ├── ProfileController.java
            │   ├── JourneyController.java
            │   └── LogController.java
            └── response
                ├── ResponseFactory.java
                └── ResultResponse.java
```

---

## Key Layers & Responsibilities

| Layer            | Description |
|------------------|-------------|
| `domain`         | Core business logic, domain entities, and interfaces. |
| `infrastructure` | Implements repositories, controllers, and technical details (HTTP, DB, etc.). |
| `presentation`   | Handles incoming requests and prepares responses. *(Currently merged inside `infrastructure/controller`.)* |
| `shared`         | Common utilities and constants. |

---

## Technologies
- **Java 17+**
- **Spring Boot**
- **Spring Data JPA**
- **Lombok**
- **Maven/Gradle**

---

## How to Run
```bash
# Step 1: Build the project
mvn clean install

# Step 2: Run the service
./mvnw spring-boot:run
````

---

## Running Tests
```bash
./mvnw test
```

---

## Maintainers
* Team: Backend
* Contact: `<EMAIL>`

---
