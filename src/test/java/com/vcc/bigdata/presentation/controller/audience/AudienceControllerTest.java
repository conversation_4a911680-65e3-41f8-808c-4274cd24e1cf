package com.vcc.bigdata.presentation.controller.audience;

import com.vcc.bigdata.application.dto.AudienceDto;
import com.vcc.bigdata.application.dto.ProfilesResponseDto;
import com.vcc.bigdata.application.dto.result.AudienceResult;
import com.vcc.bigdata.domain.service.IAudienceService;
import com.vcc.bigdata.presentation.response.Response;
import com.vcc.bigdata.presentation.response.ResponseFactory;
import com.vcc.bigdata.presentation.response.audience.AudienceResponse;
import com.vcc.bigdata.utility.Hashings;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class AudienceControllerTest {
    @Mock
    private IAudienceService audienceService;

    @InjectMocks
    private AudienceController audienceController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetAudiences() {
        List<AudienceDto> mockList = Collections.singletonList(new AudienceDto());
        when(audienceService.getAll(anyLong(), anyInt(), anyString(), anyInt(), anyInt(), any(), any(), any(), any(), anyInt()))
                .thenReturn(new AudienceResponse<>(mockList));
        AudienceResponse<List<AudienceDto>> response = audienceController.getAudiences("user1", 0, "created_at:asc", null, 0, 10, null, null, null, 0);
        assertNotNull(response);
        assertEquals(1, response.getData().size());
    }

    @Test
    void testGetAudience() {
        AudienceDto dto = new AudienceDto();
        when(audienceService.getAudience(anyLong(), anyLong())).thenReturn(dto);
        Response response = audienceController.getAudience("1", "user1");
        assertNotNull(response);
        assertEquals("Get success", response.getMessage());
    }

    @Test
    void testCreateAudience() {
        AudienceDto dto = new AudienceDto();
        when(audienceService.createAudience(any(AudienceDto.class))).thenReturn(dto);
        Response response = audienceController.createAudience(dto, "user1");
        assertNotNull(response);
        assertEquals("Create success", response.getMessage());
    }

    @Test
    void testUpdateAudience() {
        AudienceDto dto = new AudienceDto();
        when(audienceService.updateAudience(any(AudienceDto.class))).thenReturn(dto);
        Response response = audienceController.updateAudience("1", dto, "user1");
        assertNotNull(response);
        assertEquals("Update success", response.getMessage());
    }

    @Test
    void testDeleteAudience() {
        doNothing().when(audienceService).deleteAudience(anyLong(), anyLong());
        Response response = audienceController.deleteAudience("1", "user1");
        assertNotNull(response);
        assertEquals("Delete success", response.getMessage());
    }

    @Test
    void testEstimate() {
        AudienceDto dto = new AudienceDto();
        AudienceResult result = new AudienceResult();
        when(audienceService.estimate(any(AudienceDto.class))).thenReturn(result);
        Response response = audienceController.estimate(dto, "user1");
        assertNotNull(response);
        assertEquals("Estimate success", response.getMessage());
    }

    @Test
    void testPreviewProfiles() {
        AudienceDto dto = new AudienceDto();
        ProfilesResponseDto profilesResponseDto = new ProfilesResponseDto();
        when(audienceService.previewProfiles(any(AudienceDto.class))).thenReturn(profilesResponseDto);
        Response response = audienceController.previewProfiles(dto, "user1");
        assertNotNull(response);
        assertEquals("Preview success", response.getMessage());
    }

    @Test
    void testDetailProfiles() {
        ProfilesResponseDto profilesResponseDto = new ProfilesResponseDto();
        when(audienceService.detailProfiles(anyInt(), anyInt(), anyBoolean(), anyLong(), anyLong()))
                .thenReturn(new AudienceResponse<>(profilesResponseDto));
        AudienceResponse<ProfilesResponseDto> response = audienceController.detailProfiles("user1", "1", 0, 10, false);
        assertNotNull(response);
    }

    @Test
    void testCheckName() {
        when(audienceService.checkNameExists(anyLong(), anyString())).thenReturn(true);
        Response response = audienceController.checkName("user1", "testName");
        assertNotNull(response);
        assertEquals("Check segmentation name success", response.getMessage());
    }

    @Test
    void testGetTotalProfiles() {
        Map<Long, Long> result = Collections.singletonMap(1L, 5L);
        when(audienceService.getTotalProfilesOfAudience(anyLong(), anyList())).thenReturn(result);
        Response response = audienceController.getTotalProfiles("user1", Collections.singletonList(1L));
        assertNotNull(response);
        assertEquals("Get success", response.getMessage());
    }
}
