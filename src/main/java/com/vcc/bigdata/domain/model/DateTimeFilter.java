package com.vcc.bigdata.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DateTimeFilter {
    private Integer year;
    private Integer month;
    private Integer day;
    private Integer hour;
    private Integer minute;
    private Integer second;

    /**
     * Get the number of available fields
     * 
     * @return the number of available fields
     * 
     *         Example:
     *         - year, month, day are available -> return 3
     *         - year, month are available -> return 2
     *         - year is the only available field -> return 1
     *         - month, hour, second are available -> return 3
     *         - no available field -> return 0
     */
    public int getNumberAvailableField() {
        int count = 0;
        if (year != null) {
            count++;
        }
        if (month != null) {
            count++;
        }
        if (day != null) {
            count++;
        }
        if (hour != null) {
            count++;
        }
        if (minute != null) {
            count++;
        }
        if (second != null) {
            count++;
        }
        return count;
    }

    /**
     * Check if there is only one available field
     * 
     * @return true if there is only one available field, false otherwise
     * 
     *         Example:
     *         - year is the only available field -> return true
     *         - year, month are available -> return false
     *         - no available field -> return false
     */
    public boolean hasOnlyOneFieldAvailable() {
        return getNumberAvailableField() == 1;
    }

    /**
     * Check if the available fields are continuous from field "year"
     * 
     * @return true if the available fields are continuous from field "year", false
     *         otherwise
     * 
     *         Example:
     *         - year, month, day -> return true
     *         - year, month -> return true
     *         - year, day -> return false
     *         - month, day -> return false
     */
    public boolean isContinuous() {
        return getNumberAvailableField() == getLastIndexAvailableField() + 1;
    }

    /**
     * Get the index of the last available field
     * 
     * @return the index of the last available field
     * 
     *         Example:
     *         - second is the last available field -> return 5
     *         - minute is the last available field -> return 4
     *         - hour is the last available field -> return 3
     *         - day is the last available field -> return 2
     *         - month is the last available field -> return 1
     *         - year is the last available field -> return 0
     *         - no available field -> return -1
     */
    public int getLastIndexAvailableField() {
        if (second != null) {
            return 5;
        }
        if (minute != null) {
            return 4;
        }
        if (hour != null) {
            return 3;
        }
        if (day != null) {
            return 2;
        }
        if (month != null) {
            return 1;
        }
        if (year != null) {
            return 0;
        }
        return -1;
    }

    /**
     * Check if the index is the last available field
     * 
     * @param index the index to check
     * @return true if the index is the last available field, false otherwise
     */
    public boolean isLastIndexAvailableField(int index) {
        return getLastIndexAvailableField() == index;
    }
}
