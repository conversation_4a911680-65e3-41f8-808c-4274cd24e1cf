package com.vcc.bigdata.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

@Getter
@AllArgsConstructor
public class UnificationInfoKafka {
    @JsonProperty("user_id")
    private String userId;
    @JsonProperty("profile_id")
    private String profileId;
    @JsonProperty("is_delete")
    private int isDelete;
    private Set<String> sources;

}
