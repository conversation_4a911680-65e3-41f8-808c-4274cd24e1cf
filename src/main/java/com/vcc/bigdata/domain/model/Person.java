package com.vcc.bigdata.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.vcc.bigdata.application.dto.ProfileManagementDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class Person {
    @JsonProperty("profile_id")
    private String profileId;

    @JsonProperty("last_update")
    private Long lastUpdate;
    /**
     * delete status of profile, 1: delete, 0: not delete
     */
    @JsonProperty("is_delete")
    private Integer isDelete;

    private Map<String, List<Element>> data;
    private List<Behavior> behaviors;
    private List<BehaviorV2> behaviorsV2;
    private Insight insight;
    private InsightV2 insightV2;
    private Session session;
    private List<String> filters;

    private Set<String> sources;
    // external data
    @JsonProperty("profile_name")
    private String profileName = "Anonymous";
    @JsonProperty("source_info")
    private Map<String, String> sourceInfo;
    @JsonProperty("profile_preview")
    private Map<String, List<Object>> profilePreview;
    @JsonProperty("data_labels")
    private Set<String> dataLabels;
    @JsonProperty("segments")
    private List<Map<String, String>> segments;
    @JsonProperty("scoring")
    private Scoring scoring;
    @JsonProperty("profile_mapping")
    private List<ProfileManagementDto.Mapping> mapping;

    public Person(String profileId) {
        this.profileId = profileId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
//    @JsonInclude(JsonInclude.Include.ALWAYS)
    public static class Element {
        private Object value;
        private Set<String> sources;
        //List lưu các id của các connector tương ứng với sources
        private List<Integer> connectorId;
        // external info
        @JsonProperty("field_name")
        private String fieldName;

        @JsonProperty("is_masking")
        private Boolean isMasking;

        private String demographic;

        @JsonProperty("data_type")
        private String dataType;

        public Element(Object value, Set<String> sources) {
            this.value = value;
            this.sources = sources;
            this.fieldName = null;
            this.isMasking = null;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Scoring {

        @JsonProperty(value = "engagement_score")
        private Integer engagementScore;

        @JsonProperty(value = "data_quality_score")
        private Double dataQualityScore;

        @JsonProperty(value = "loyalty_score")
        private Double loyaltyScore;

        @JsonProperty(value = "lead_score")
        private Integer leadScore;

        @JsonProperty(value = "explain")
        private Explain explain;

        @JsonProperty(value = "explain_data_quality")
        private ExplainDataQualityScore explainDataQualityScore;

        @JsonProperty(value = "explain_loyalty")
        private ExplainLoyaltyScore explainLoyaltyScore;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @JsonIgnoreProperties(ignoreUnknown = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class Explain {

            @JsonProperty(value = "awareness")
            private int awareness;

            @JsonProperty(value = "attraction")
            private int attraction;

            @JsonProperty(value = "action")
            private int action;

            @JsonProperty(value = "advocacy")
            private int advocacy;

            @JsonProperty(value = "negativeness")
            private int negativeness;
        }

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @JsonIgnoreProperties(ignoreUnknown = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class ExplainDataQualityScore {

            @JsonProperty(value = "fullname")
            private int fullname;

            @JsonProperty(value = "email")
            private int email;

            @JsonProperty(value = "phone")
            private int phone;

            @JsonProperty(value = "guid")
            private int guid;

            @JsonProperty(value = "facebook_id")
            private int facebookId;

            @JsonProperty(value = "youtube_id")
            private int youtubeId;

            @JsonProperty(value = "tiktok_id")
            private int tiktokId;

            @JsonProperty(value = "gender")
            private int gender;

            @JsonProperty(value = "location")
            private int location;

            @JsonProperty(value = "birthday")
            private int dob;
        }

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @JsonIgnoreProperties(ignoreUnknown = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class ExplainLoyaltyScore {

            @JsonProperty(value = "time_on_read")
            private String tor;

            @JsonProperty(value = "freq_read")
            private List<String> freqRead;

            @JsonProperty(value = "routine")
            private List<String> routine;

            @JsonProperty(value = "play_turn")
            private int playTurn;

            @JsonProperty(value = "watch_time")
            private int watchTime;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class Behavior {
        private Integer type;
        private String sourceName;
        private String message;
        private String value;
        private String url;
        private String location;
        private String firstName;
        private String email;
        private String loginId;
        private String loginProvider;

        public Behavior(Integer type) {
            this.type = type;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class BehaviorV2 {
        private String date;
        private String time;
        private String url;
        private Integer type; //=action data: click or view or...
        private String message; //=content
        private String sourceName;
        private Integer connectorType;
        private String firstName;
        private String loginId;
        private String loginProvider;

        public BehaviorV2(Integer type) {
            this.type = type;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BehaviorV2Filter {
        @JsonProperty("filter_hashtag")
        private List<String> filterHashtag = new ArrayList<>();
        @JsonProperty("start_date")
        private String startDate;
        @JsonProperty("end_date")
        private String endDate;
        @JsonProperty("filter_hnh")
        private Integer filterHnH = 1; //default
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class Insight {
        private List<String> topic;
        private List<String> routine;
        private List<String> typeNews;
        private String meanTor;
        private List<String> freqRead;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class InsightV2 {
        private List<String> favTopics;
        private Integer numberNews;
        private Integer numberVideo;
        private Integer click;
        private Integer share; //news ko có
        private List<String> routine; //time frame for reading
        private String meanTor; //mean time reading
        private String meanView; //mean time viewing (Lấy ở frequency)

//        //2 trường này để tính loyal score
//        private List<String> fredRead;
//        private String playTurn;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Session {
        private String metric;
        private String observer;
        private String lastSeen;
        private String ip;
        private String location;
        private String referSource;
        private String referUrl;
        private String touchPoint;
    }

}
