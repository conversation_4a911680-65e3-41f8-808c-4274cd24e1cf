package com.vcc.bigdata.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UnificationData {
    @JsonProperty("source_id")
    private String sourceId;

    @JsonProperty("user_id_profile_management")
    private String userIdProfileManagement;

    @JsonProperty("mapping_confirm")
    private List<MappingUnification> mappingConfirm;

    @JsonProperty("rule_unification")
    private Rule ruleUnification;

    private List<JsonNode> data;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Rule {
        private String ex;

        private List<String> field;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class MappingUnification {
        @JsonProperty("field_source")
        private String fieldSource; //= field name

        @JsonProperty("field_mapping")
        private String fieldMapping; //hashcode của field_source

        @JsonProperty("data_type")
        private String dataType;

        private String demographic;
    }
}