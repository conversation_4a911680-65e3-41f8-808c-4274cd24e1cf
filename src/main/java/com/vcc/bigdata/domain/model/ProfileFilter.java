package com.vcc.bigdata.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ProfileFilter {
    @JsonProperty("user_id")
    private Long userId;

    @JsonProperty("profile_management_id")
    private String profileManagementId;

    private Integer page = 0;

    private Integer limit = 30;

    @JsonProperty("field_search")
    private Map<String, Object> fieldSearch;
}
