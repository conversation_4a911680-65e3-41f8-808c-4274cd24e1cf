package com.vcc.bigdata.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.vcc.bigdata.utility.converter.JsonNodeBlobConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity(name = "audiences")
public class Audience {
    @Id
    private Long id;

    private String name;

    private String description;

    @Column(name = "user_id")
    @JsonProperty("user_id")
    private Long userId;

    @Column(name = "is_deleted")
    @JsonProperty("is_deleted")
    private Integer isDeleted;

    @Column(name = "created_at", updatable = false)
    @JsonProperty("created_at")
    @CreationTimestamp
    private Timestamp createdAt;

    @Column(name = "updated_at")
    @JsonProperty("updated_at")
    @UpdateTimestamp
    private Timestamp updatedAt;

    @Column(name = "conditions")
    @JsonProperty("segment_rule")
    @Convert(converter = JsonNodeBlobConverter.class)
    private JsonNode segmentRule;

    @Column(name = "profile_count")
    @JsonProperty("profile_count")
    private Long profileCount;

    private Integer type;
}
