package com.vcc.bigdata.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.vcc.bigdata.utility.converter.JsonNodeBlobConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity(name = "profile_management")
public class ProfileManagement {
    @Id
    private Long id;

    private String name;

    @Column(name = "user_id")
    @JsonProperty("user_id")
    private Long userId;

    @Column(name = "create_time", updatable = false)
    @JsonProperty("create_time")
    @CreationTimestamp
    private Timestamp createTime;

    @Column(name = "update_time")
    @JsonProperty("update_time")
    @UpdateTimestamp
    private Timestamp updateTime;

    @Convert(converter = JsonNodeBlobConverter.class)
    private JsonNode mapping;


    @Convert(converter = JsonNodeBlobConverter.class)
    private JsonNode sources;

    private Integer status;

    @Column(name = "kms_customer_id")
    @JsonProperty("kms_customer_id")
    private String kmsCustomerId;
}
