package com.vcc.bigdata.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
@Builder
public class PagingPayload<T> {
    private List<T> data;
    private int limit;
    private int offset;
    private long total;

    public static <T> PagingPayload<T> empty() {
        return PagingPayload.<T>builder()
                .data(new ArrayList<>())
                .build();
    }
}
