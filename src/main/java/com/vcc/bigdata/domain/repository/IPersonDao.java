package com.vcc.bigdata.domain.repository;

import com.vcc.bigdata.domain.model.Pair;
import com.vcc.bigdata.domain.model.Person;
import com.vcc.bigdata.domain.model.ProfileFilter;
import com.vcc.bigdata.domain.model.UnificationData;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IPersonDao {
    void update(String userId, Person person, String kmsCustomerId);
    void updateMergePerson(String userId, Person person, String kmsCustomerId, List<Person> deletedPersons);
    Person searchByRuleUnify(String userId, PersonSearch personSearch, UnificationData.Rule rule);
    List<Person> searchAllByRuleUnify(String userId, PersonSearch personSearch, UnificationData.Rule rule);

    Pair<List<Person>, Long> search(ProfileFilter profileFilter);

    Pair<List<Person>, Long> searchProfileMultipleIndices(ProfileFilter profileFilter);

    List<String> getProfileIdByValueField(String userId, Map<String, String> fieldValue);

    List<Map<String, String>> getSampleByField(String userId, BoolQueryBuilder boolQueryBuilder, List<String> fields);

    void delete(String userId, String profileId);

    Long getTotal(String userId);

    Long getTotalBySource(String userId, String sourceId);

    Long estimateSegment(String userId, BoolQueryBuilder boolQueryBuilder);

    List<Person> getBySegment(String userId, BoolQueryBuilder boolQueryBuilder, Integer page, Integer limit);

    List<Person> getBySegmentV2(String userId, BoolQueryBuilder boolQueryBuilder);

    Person getById(String userId, String profileId);

    List<Person> getBatchProfileFromHbase(String userId, List<String> profileIds);

    Person getLatestModifiedPersonById(String userId, String profileId);

    List<String> getSuggestionValue(String userId, String fieldName, String value);

    Set<String> getAllProfileInSegment(String userId, BoolQueryBuilder boolQueryBuilder);

}
