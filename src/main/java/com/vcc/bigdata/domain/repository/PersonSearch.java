package com.vcc.bigdata.domain.repository;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.vcc.bigdata.domain.model.Person;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PersonSearch {
    @JsonProperty("profile_id")
    private String profileId;

    @JsonProperty("last_update")
    private Long lastUpdate;

    private Map<String, List<Object>> data;

    private Set<String> sources;
    @JsonProperty("is_delete")
    private Integer isDelete;

    public PersonSearch(Person person) {
        this.profileId = person.getProfileId();
        this.lastUpdate = person.getLastUpdate();

        this.data = new HashMap<>();
        person.getData().forEach((key, value) -> {
            this.data.put(key, new ArrayList<>());
            value.forEach(element -> this.data.get(key).add(element.getValue()));
        });

        this.sources = person.getSources();
        this.isDelete = person.getIsDelete();
    }
}
