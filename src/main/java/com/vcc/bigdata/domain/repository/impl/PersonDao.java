package com.vcc.bigdata.domain.repository.impl;

import com.vcc.bigdata.domain.repository.PersonSearch;
import com.vcc.bigdata.shared.Constants;
import com.vcc.bigdata.domain.repository.IPersonDao;
import com.vcc.bigdata.domain.model.*;
import com.vcc.bigdata.domain.service.IKmsService;
import com.vcc.bigdata.infrastructure.impl.read.ElasticsearchReadAdapter;
import com.vcc.bigdata.infrastructure.impl.read.HBaseReadAdapter;
import com.vcc.bigdata.infrastructure.impl.write.ElasticsearchWriteAdapter;
import com.vcc.bigdata.infrastructure.impl.write.HBaseWriteAdapter;
import com.vcc.bigdata.infrastructure.impl.write.KafkaBrokerWriter;
import com.vcc.bigdata.utility.Strings;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AbstractAggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

@Component
@Slf4j
public class PersonDao implements IPersonDao {
    private final ElasticsearchReadAdapter elasticsearchReadAdapter;
    private final ElasticsearchWriteAdapter elasticsearchWriteAdapter;
    private final IKmsService kmsService;
    private final HBaseReadAdapter<Person> hBaseReadAdapter;
    private final HBaseWriteAdapter<Person> hBaseWriteAdapter;
    private final KafkaBrokerWriter kafkaBrokerWriter;
    private final String PREFIX_PERSON_INDEX;
    private final String PREFIX_PERSON_TABLE;
    private final String PREFIX_PERSON_MODIFY_TABLE;
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final String PREFIX_MODIFY_PERSON_INDEX_HNH;
    private final String userIdHouseNHome = "3046799161";

    public PersonDao(ElasticsearchReadAdapter elasticsearchReadAdapter,
                     ElasticsearchWriteAdapter elasticsearchWriteAdapter,
                     IKmsService kmsService,
                     @Qualifier("hbasePersonReadAdapter") HBaseReadAdapter<Person> hBaseReadAdapter,
                     @Qualifier("hbasePersonWriteAdapter") HBaseWriteAdapter<Person> hBaseWriteAdapter,
                     @Qualifier("unifyKafkaBrokerWriter") KafkaBrokerWriter kafkaBrokerWriter,
                     @Value("${elastic.prefix.profile.person}") String prefixPersonIndex,
                     @Value("${hbase.prefix.profile.person}") String prefixPersonTable,
                     @Value("${hbase.prefix.profile.person.modified}") String prefixPersonModifyTable,
                     @Value("${elastic.prefix.modify.profile.person-hnh}")String prefixModifyPersonIndex) {
        this.elasticsearchReadAdapter = elasticsearchReadAdapter;
        this.elasticsearchWriteAdapter = elasticsearchWriteAdapter;
        this.kmsService = kmsService;
        this.hBaseReadAdapter = hBaseReadAdapter;
        this.hBaseWriteAdapter = hBaseWriteAdapter;
        this.kafkaBrokerWriter = kafkaBrokerWriter;
        PREFIX_PERSON_INDEX = prefixPersonIndex;
        PREFIX_PERSON_TABLE = prefixPersonTable;
        PREFIX_PERSON_MODIFY_TABLE = prefixPersonModifyTable;
        PREFIX_MODIFY_PERSON_INDEX_HNH = prefixModifyPersonIndex;
    }

    private List<Person> getPeopleFromHBase(String userId, List<String> ids) {
        if (ids.isEmpty()) return new ArrayList<>();

        List<Person> people = new ArrayList<>();
        List<JSONObject> data = hBaseReadAdapter.getAll(PREFIX_PERSON_TABLE + userId, ids, "person");
        for (JSONObject object : data) {
            try {
                people.add(Constants.SERIALIZER.readValue(object.toString(), Person.class));
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        return people;
    }

    private List<Person> getPeopleFromTablesHBase(String userId, List<String> ids) {
        if (ids.isEmpty()) return new ArrayList<>();

        List<Person> people = new ArrayList<>();
        String prefix = "modify_";
        List<String> profileIds = new ArrayList<>();
        List<String> modifiedProfileIds = new ArrayList<>();
        for(String id: ids){
            if(id.startsWith(prefix))
                modifiedProfileIds.add(id.substring(prefix.length()));
            else
                profileIds.add(id);
        }
        profileIds.removeAll(modifiedProfileIds);

        List<JSONObject> data = new ArrayList<>();
        if(!profileIds.isEmpty()) data = hBaseReadAdapter.getAll(PREFIX_PERSON_TABLE + userId, profileIds, "person");
        if(!modifiedProfileIds.isEmpty()) data.addAll(hBaseReadAdapter.getAll(PREFIX_PERSON_MODIFY_TABLE, modifiedProfileIds, "person"));

        for (JSONObject object : data) {
            try {
                people.add(Constants.SERIALIZER.readValue(object.toString(), Person.class));
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        return people;
    }

    private Person getPersonFromHBase(String userId, String id) {
        JSONObject object = hBaseReadAdapter.getById(PREFIX_PERSON_TABLE + userId, id, "person");
        try {
            return Constants.SERIALIZER.readValue(object.toString(), Person.class);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * get list profile from HBase use userId and list profileId
     * @return List of {@link Person}
     */
    private List<Person> getListPersonFromHBase(String userId, List<String> ids) {
        List<Person> result = new ArrayList<>();
        List<JSONObject> objectList = hBaseReadAdapter.getAll(PREFIX_PERSON_TABLE + userId, ids, "person");
        for (JSONObject object : objectList) {
            try {
                Person person = Constants.SERIALIZER.readValue(object.toString(), Person.class);
                result.add(person);
            } catch (Exception e) {
                logger.error("Error when serialize person from hbase " + e.getMessage(), e);
            }
        }
        return result;
    }

    private Person getLatestModifiedPersonFromHBase(String profileId) {
        JSONObject object = hBaseReadAdapter.getById(PREFIX_PERSON_MODIFY_TABLE, profileId, "person");
        try {
            return Constants.SERIALIZER.readValue(object.toString(), Person.class);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void update(String userId, Person person, String kmsCustomerId) {
        person.setLastUpdate(System.currentTimeMillis());

        // Chỉ lưu updated profile data cho user house n home
        try {
            PersonSearch personSearch = new PersonSearch(person);
            if(userId.equals(userIdHouseNHome)) {
                elasticsearchWriteAdapter.getClient().index(
                        new IndexRequest(PREFIX_MODIFY_PERSON_INDEX_HNH)
                                .id("modify_" + personSearch.getProfileId())
                                .source(
                                        Constants.SERIALIZER.writeValueAsString(personSearch),
                                        XContentType.JSON
                                ),
                        RequestOptions.DEFAULT);
            } else {
                String personSearchSerialized = Constants.SERIALIZER.writeValueAsString(personSearch);
                elasticsearchWriteAdapter.getClient().index(
                        new IndexRequest(PREFIX_PERSON_INDEX + userId)
                                .id(personSearch.getProfileId())
                                .source(
                                        personSearchSerialized,
                                        XContentType.JSON
                                ),
                        RequestOptions.DEFAULT);
            }
        } catch (Exception e) {
            logger.error("Error when update person: {}", person);
            logger.error(e.getMessage(), e);
        }


        try {
            // encrypt and save to HBase
            Map<String, String> dataStorage = new HashMap<>();
            dataStorage.put("profile_id", person.getProfileId());
            dataStorage.put("last_update", String.valueOf(person.getLastUpdate()));
            dataStorage.put("sources", Constants.SERIALIZER.writeValueAsString(person.getSources()));
            dataStorage.put("is_delete", String.valueOf(person.getIsDelete()));

            for (Map.Entry<String, List<Person.Element>> entry : person.getData().entrySet()) {
                if (entry.getKey().endsWith("_t")) {
                    continue;
                }
                String key = "_" + entry.getKey();
                List<Person.Element> value = new ArrayList<>();
                if (entry.getValue() == null) {
                    logger.warn("Skipping entry with null value for key: {}", key);
                    continue;
                }

                entry.getValue().forEach(element -> {
                    if (element != null && element.getValue() != null && element.getSources() != null) {
                        value.add(new Person.Element(kmsService.encrypt(kmsCustomerId, element.getValue().toString()), element.getSources()));
                    } else {
                        logger.warn("Skipping element with null value or sources for key: {}", key);
                    }
                });
                dataStorage.put(key, Constants.SERIALIZER.writeValueAsString(value));
            }


            hBaseWriteAdapter.addProfile(PREFIX_PERSON_TABLE + userId, person, dataStorage);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        try {
            // send unify to kafka
            UnificationInfoKafka infoKafka = new UnificationInfoKafka(userId, person.getProfileId(), 0, person.getSources());
            kafkaBrokerWriter.write(Constants.SERIALIZER.writeValueAsString(infoKafka));
        } catch (Exception e) {
            logger.error("Error when send unify profile to kafka" + e.getMessage(), e);
        }
    }

    /**
     * update profile when unity, contains:
     * <br> -  save new merge profile
     * <br> -  delete list old profile before merge
     * @param person new merge profile
     * @param deletedPersons list profile need mark delete
     */
    @Override
    public void updateMergePerson(String userId, Person person, String kmsCustomerId, List<Person> deletedPersons) {
        update(userId, person, kmsCustomerId);
        if(!deletedPersons.isEmpty()) deletePerson(userId, deletedPersons);
    }

    /**
     * delete profiles when merge multi profile
     * <br> save data Elasticsearch with status {@link PersonSearch#getIsDelete()} = 1
     * <br> save profile HBase with status {@link PersonSearch#getIsDelete()} = 1
     * <br> send profile kafka with status {@link PersonSearch#getIsDelete()} = 1
     * @param deletedPersons List of {@link Person} need mark delete
     */
    private void deletePerson(String userId, List<Person> deletedPersons) {

        // Chỉ lưu updated profile data cho user house n home
        try {
            if(userId.equals(userIdHouseNHome)) {

                BulkRequest bulkRequest = new BulkRequest();

                for (Person deletePerson : deletedPersons) {
                    PersonSearch deletePersonSearch = new PersonSearch(deletePerson);
                    IndexRequest indexRequest = new IndexRequest(PREFIX_MODIFY_PERSON_INDEX_HNH + userId)
                            .id("modify_" + deletePerson.getProfileId())
                            .source(Constants.SERIALIZER.writeValueAsString(deletePersonSearch), XContentType.JSON);

                    bulkRequest.add(indexRequest);
                }

                BulkResponse bulkResponse = elasticsearchWriteAdapter.getClient().bulk(bulkRequest, RequestOptions.DEFAULT);

                if (bulkResponse.hasFailures()) {
                    for (BulkItemResponse itemResponse : bulkResponse.getItems()) {
                        if (itemResponse.isFailed()) {
                            logger.warn("Error when unify save data ES to user {}, Failed request: {}", userId, itemResponse.getFailureMessage());
                        }
                    }
                }
            }else{

                BulkRequest bulkRequest = new BulkRequest();

                for (Person deletePerson : deletedPersons) {
                    PersonSearch deletePersonSearch = new PersonSearch(deletePerson);
                    IndexRequest indexRequest = new IndexRequest(PREFIX_PERSON_INDEX + userId)
                            .id(deletePerson.getProfileId())
                            .source(Constants.SERIALIZER.writeValueAsString(deletePersonSearch), XContentType.JSON);

                    bulkRequest.add(indexRequest);
                }

                BulkResponse bulkResponse = elasticsearchWriteAdapter.getClient().bulk(bulkRequest, RequestOptions.DEFAULT);

                if (bulkResponse.hasFailures()) {
                    for (BulkItemResponse itemResponse : bulkResponse.getItems()) {
                        if (itemResponse.isFailed()) {
                            logger.warn("Error when unify save data ES to user {}, Failed request: {}", userId, itemResponse.getFailureMessage());
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        try {
            // encrypt and save to HBase
            List<Map<String, String>> dataStorageList = new ArrayList<>();
            deletedPersons.forEach(person -> {
                try {
                    Map<String, String> dataStorage = new HashMap<>();
                    dataStorage.put("profile_id", person.getProfileId());
                    dataStorage.put("last_update", String.valueOf(person.getLastUpdate()));
                    dataStorage.put("sources", Constants.SERIALIZER.writeValueAsString(person.getSources()));
                    dataStorage.put("is_delete", String.valueOf(person.getIsDelete()));
                    dataStorageList.add(dataStorage);
                } catch (Exception e) {
                    logger.error("Error when get sources to save HBase " +e.getMessage(), e);
                }
            });

            hBaseWriteAdapter.updateListProfile(PREFIX_PERSON_TABLE + userId, deletedPersons, dataStorageList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        try {
            // send unify to kafka
            List<String> kafkaDatas = new ArrayList<>();
            deletedPersons.forEach(person -> {
                UnificationInfoKafka infoKafka = new UnificationInfoKafka(userId, person.getProfileId(), 1, person.getSources());
                try {
                    kafkaDatas.add(Constants.SERIALIZER.writeValueAsString(infoKafka));
                } catch (Exception e) {
                    logger.error("Error when send unify profile to kafka" + e.getMessage(), e);
                }
            });

            kafkaBrokerWriter.write(kafkaDatas);
        } catch (Exception e) {
            logger.error("Error when send unify profile to kafka" + e.getMessage(), e);
        }

    }

    @Override
    public Person searchByRuleUnify(String userId, PersonSearch personSearch, UnificationData.Rule rule) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (rule.getEx().equals("or")) {
            rule.getField().forEach(field -> {
                if (personSearch.getData().containsKey(field)) {
                    personSearch.getData().get(field).forEach(value -> {
                        if (value instanceof String)
                            queryBuilder.should(QueryBuilders.termQuery("data." + field + ".keyword", value));
                        else queryBuilder.should(QueryBuilders.termQuery("data." + field, value));
                    });
                }
            });
            queryBuilder.minimumShouldMatch(1);
        } else {
            rule.getField().forEach(field -> {
                if (personSearch.getData().containsKey(field)) {
                    personSearch.getData().get(field).forEach(value -> {
                        if (value instanceof String)
                            queryBuilder.must(QueryBuilders.termQuery("data." + field + ".keyword", value));
                        else queryBuilder.must(QueryBuilders.termQuery("data." + field, value));
                    });
                }
            });
        }

        try {
            SearchResponse response = elasticsearchReadAdapter.search(PREFIX_PERSON_INDEX + userId, queryBuilder, null, 1, 0);
            if (response != null && response.getHits() != null && response.getHits().getHits() != null && response.getHits().getHits().length > 0) {
                String profileId = response.getHits().getHits()[0].getId();
                return getPersonFromHBase(userId, profileId);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * Find list of profiles that satisfy the unify rule
     * <br> -  build query by unify rule and except deleted profile (is_delete = 1)
     * <br> -  search Elasticsearch with page 0 and limit 1000 to ensure that all satisfying data is found
     * <br> -  get
     * @param userId
     * @param personSearch
     * @param rule
     * @return
     */
    @Override
    public List<Person> searchAllByRuleUnify(String userId, PersonSearch personSearch, UnificationData.Rule rule) {
        BoolQueryBuilder searchQueryBuilder = new BoolQueryBuilder();
        searchQueryBuilder.mustNot(QueryBuilders.termQuery("is_delete", 1));
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (rule.getEx().equals("or")) {
            rule.getField().forEach(field -> {
                if (personSearch.getData().containsKey(field)) {
                    personSearch.getData().get(field).forEach(value -> {
                        if (value instanceof String)
                            queryBuilder.should(QueryBuilders.termQuery("data." + field + ".keyword", value));
                        else queryBuilder.should(QueryBuilders.termQuery("data." + field, value));
                    });
                }
            });
            queryBuilder.minimumShouldMatch(1);
        } else {
            rule.getField().forEach(field -> {
                if (personSearch.getData().containsKey(field)) {
                    personSearch.getData().get(field).forEach(value -> {
                        if (value instanceof String)
                            queryBuilder.must(QueryBuilders.termQuery("data." + field + ".keyword", value));
                        else queryBuilder.must(QueryBuilders.termQuery("data." + field, value));
                    });
                }
            });
        }
        searchQueryBuilder.must(queryBuilder);
        try {
            SearchResponse response = elasticsearchReadAdapter.search(PREFIX_PERSON_INDEX + userId, searchQueryBuilder, null, 1000, 0);
            if (response != null && response.getHits() != null && response.getHits().getHits() != null && response.getHits().getHits().length > 0) {
                List<String> profileIds = new ArrayList<>();
                for (SearchHit hit : response.getHits()) {
                    profileIds.add(hit.getId());
                }
                return getListPersonFromHBase(userId, profileIds);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return new ArrayList<>();
    }

    @Override
    public Pair<List<Person>, Long> search(ProfileFilter profileFilter) {
        List<String> ids = new ArrayList<>();

        BoolQueryBuilder queryBuilder = null;
        if (profileFilter.getFieldSearch() != null) {
            queryBuilder = new BoolQueryBuilder();
            for (Map.Entry<String, Object> entry : profileFilter.getFieldSearch().entrySet()) {
                if (entry.getValue() instanceof String)
                    queryBuilder.should(QueryBuilders.wildcardQuery("data." + entry.getKey(), "*" + entry.getValue() + "*").caseInsensitive(true));
                else
                    queryBuilder.should(QueryBuilders.termQuery("data." + entry.getKey(), entry.getValue()).caseInsensitive(true));
            }
            queryBuilder.minimumShouldMatch(1);
        }
        Pair<String, SortOrder> sort = new Pair<>("last_update", SortOrder.DESC);

        long total = 0L;
        try {
            SearchResponse response = elasticsearchReadAdapter.search(PREFIX_PERSON_INDEX + profileFilter.getUserId(), queryBuilder, Collections.singletonList(sort), profileFilter.getLimit(), profileFilter.getPage());
            for (SearchHit searchHit : response.getHits().getHits()) ids.add(searchHit.getId());

            CountResponse countResponse = elasticsearchReadAdapter.countingDocument(PREFIX_PERSON_INDEX + profileFilter.getUserId(), queryBuilder);
            total = countResponse.getCount();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return new Pair<>(getPeopleFromHBase(String.valueOf(profileFilter.getUserId()), ids), total);
    }

    /**
     * Hàm tìm kiếm profile trên 2 index: index PREFIX_PERSON_INDEX + user-id tương ứng và index
     * PREFIX_MODIFY_PERSON_INDEX_HNH lưu dữ liệu modified profile
     **/
    @Override
    public Pair<List<Person>, Long> searchProfileMultipleIndices(ProfileFilter profileFilter) {
        List<String> ids = new ArrayList<>();

        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.mustNot(QueryBuilders.termQuery("is_delete", 1));
        if (profileFilter.getFieldSearch() != null) {
            for (Map.Entry<String, Object> entry : profileFilter.getFieldSearch().entrySet()) {
                if (entry.getValue() instanceof String)
                    queryBuilder.should(QueryBuilders.wildcardQuery("data." + entry.getKey() + ".keyword", "*" + entry.getValue() + "*").caseInsensitive(true));
                else
                    queryBuilder.should(QueryBuilders.termQuery("data." + entry.getKey(), entry.getValue()).caseInsensitive(true));
            }
            queryBuilder.minimumShouldMatch(1);
        }
        Pair<String, SortOrder> sort = new Pair<>("last_update", SortOrder.DESC);

        long total = 0L;
        try {
            String[] indices = getIndicesArray(String.valueOf(profileFilter.getUserId()));

            SearchResponse response = elasticsearchReadAdapter.searchAcrossMultipleIndices(indices, queryBuilder, Collections.singletonList(sort), profileFilter.getLimit(), profileFilter.getPage());

            for (SearchHit searchHit : response.getHits().getHits()) ids.add(searchHit.getId());

            CountResponse countResponse = elasticsearchReadAdapter.countingDocument(PREFIX_PERSON_INDEX + profileFilter.getUserId(), queryBuilder);
            total = countResponse.getCount();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return new Pair<>(getPeopleFromTablesHBase(String.valueOf(profileFilter.getUserId()), ids), total);
    }

    @Override
    public List<String> getProfileIdByValueField(String userId, Map<String, String> fieldValue) {
        List<String> ids = new ArrayList<>();

        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.minimumShouldMatch(1);
        for (Map.Entry<String, String> entry : fieldValue.entrySet()) {
            queryBuilder.should(QueryBuilders.termQuery("data." + entry.getKey() + ".keyword", entry.getValue()));
        }

        Pair<String, SortOrder> sort = new Pair<>("last_update", SortOrder.DESC);

        try {
            SearchResponse response = elasticsearchReadAdapter.search(PREFIX_PERSON_INDEX + userId, queryBuilder, Collections.singletonList(sort), 10000, 0);
            for (SearchHit searchHit : response.getHits().getHits()) ids.add(searchHit.getId());
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        return ids;
    }

    @Override
    public List<Map<String, String>> getSampleByField(String userId, BoolQueryBuilder boolQueryBuilder, List<String> fields) {
        List<String> ids = new ArrayList<>();
        BoolQueryBuilder existQuery = new BoolQueryBuilder();
        fields.forEach(field -> existQuery.should(QueryBuilders.existsQuery("data." + field)));
        existQuery.minimumShouldMatch(1);

        if (boolQueryBuilder == null) boolQueryBuilder = existQuery;
        else boolQueryBuilder.must(existQuery);

        try {
            SearchResponse response = elasticsearchReadAdapter.search(PREFIX_PERSON_INDEX + userId, boolQueryBuilder, Collections.singletonList(new Pair<>("_score", SortOrder.DESC)), 10, 0);
            for (SearchHit searchHit : response.getHits().getHits()) ids.add(searchHit.getId());
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        List<Map<String, String>> result = new ArrayList<>();
        for (String id : ids) {
            Person person = getPersonFromHBase(userId, id);
            Map<String, String> data = new HashMap<>();
            for (String field : fields)
                if (person.getData().containsKey(field))
                    data.put(field, person.getData().get(field).get(0).getValue().toString());
            if (!data.isEmpty()) result.add(data);
        }

        return result;
    }

    @Override
    public void delete(String userId, String profileId) {
        try {
            DeleteRequest deleteRequest = new DeleteRequest(PREFIX_PERSON_INDEX + userId, profileId);
            elasticsearchWriteAdapter.getClient().delete(deleteRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        // delete on HBase
        hBaseWriteAdapter.delete(PREFIX_PERSON_TABLE + userId, new Person(profileId));
    }

    @Override
    public Long getTotal(String userId) {
        long total = 0L;
        try {
            CountResponse countResponse = elasticsearchReadAdapter.countingDocument(PREFIX_PERSON_INDEX + userId, null);
            total = countResponse.getCount();
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return total;
    }

    /**
     * count total profile by source and is_delete != 1
     * @param userId
     * @param sourceId
     * @return
     */
    @Override
    public Long getTotalBySource(String userId, String sourceId) {
        long total = 0L;
        try {
            BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
            queryBuilder.must(QueryBuilders.termQuery("sources.keyword", sourceId));
            queryBuilder.mustNot(QueryBuilders.termQuery("is_delete", 1));
            CountResponse countResponse = elasticsearchReadAdapter.countingDocument(PREFIX_PERSON_INDEX + userId, queryBuilder);
            total = countResponse.getCount();
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return total;
    }

    @Override
    public Long estimateSegment(String userId, BoolQueryBuilder boolQueryBuilder) {
        try {
            return elasticsearchReadAdapter.countingDocument(PREFIX_PERSON_INDEX + userId, boolQueryBuilder).getCount();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return 0L;
    }

    @Override
    public List<Person> getBySegment(String userId, BoolQueryBuilder boolQueryBuilder, Integer page, Integer limit) {
        List<String> ids = new ArrayList<>();

        try {
            // Tạo list indices
            String[] indices = getIndicesArray(userId);

            SearchResponse response = elasticsearchReadAdapter.searchAcrossMultipleIndices(indices, boolQueryBuilder, Collections.singletonList(new Pair<>("last_update", SortOrder.DESC)), limit, page);
            for (SearchHit searchHit : response.getHits().getHits()) {
                ids.add(searchHit.getId());
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return getPeopleFromTablesHBase(userId, ids);
    }

    /**
     * hàm lấy mảng indices theo user id
     * Nếu là user id housenhome thì thêm index lưu modified data của user hnh vào
     * @param userId id của user
     * @return
     */
    private String[] getIndicesArray(String userId) {
        // Tạo list indices
        List<String> indicesList = new ArrayList<>();
        indicesList.add(PREFIX_PERSON_INDEX + userId);

        // Nếu là user hnh thì search thêm cả index lưu data modified
        if (userId.equals(userIdHouseNHome)) {
            indicesList.add(PREFIX_MODIFY_PERSON_INDEX_HNH);
        }

        // Convert list to array and return
        return indicesList.toArray(new String[0]);
    }

    @Override
    public List<Person> getBySegmentV2(String userId, BoolQueryBuilder boolQueryBuilder) {
        List<String> ids = new ArrayList<>();

        try {
            RestHighLevelClient client = elasticsearchReadAdapter.getClient();
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQueryBuilder);
            searchSourceBuilder.size(1000);
            String[] indices = getIndicesArray(userId);
            SearchRequest searchRequest = new SearchRequest(indices);
            searchRequest.source(searchSourceBuilder);
            searchRequest.scroll(TimeValue.timeValueMinutes(1L));

            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            String scrollId = response.getScrollId();
            SearchHit[] searchHits = response.getHits().getHits();
            while (searchHits != null && searchHits.length > 0) {
                for (SearchHit hit: searchHits) {
                    ids.add(hit.getId());
                }
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(TimeValue.timeValueMinutes(1L));
                response = client.scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollId = response.getScrollId();
                searchHits = response.getHits().getHits();
            }
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
//            logger.error(e.getMessage());
            logger.error("Error: ", e);
        }

        String prefix = "modify_";
        List<String> profileIds = new ArrayList<>();
        List<String> modifiedProfileIds = new ArrayList<>();
        for(String id: ids){
            if(id.startsWith(prefix))
                modifiedProfileIds.add(id.substring(prefix.length()));
            else
                profileIds.add(id);
        }
        profileIds.removeAll(modifiedProfileIds);

        // get batch profile from hbase
        List<Person> results = new ArrayList<>();
        getBatchProfileFromHbase(userId, results, profileIds);
        getBatchProfileFromHbase(userId, results, modifiedProfileIds);

        return results;
    }

    //Get batch profile from hbase
    private void getBatchProfileFromHbase(String userId, List<Person> person, List<String> profileIds){
        for (int i = 0; i < profileIds.size(); i += 100) {
            int toIndex = Math.min(i + 100, profileIds.size());
            List<String> batch = profileIds.subList(i, toIndex);
            person.addAll(getPeopleFromHBase(userId, batch));
        }
    }

    @Override
    public Person getById(String userId, String profileId) {
        return getPersonFromHBase(userId, profileId);
    }

    @Override
    public List<Person> getBatchProfileFromHbase(String userId, List<String> profileIds) {
        return getPeopleFromHBase(userId, profileIds);
    }

    @Override
    public Person getLatestModifiedPersonById(String userId, String profileId) {
        //Check nếu bảng modify_profile_person nếu không có dữ liệu profile thì trả về dữ liệu profile ở bảng profile_person_{user-id} (hàm getById)
        Person person = getLatestModifiedPersonFromHBase(profileId);
        if(person == null) return getById(userId, profileId);

        return person;
    }

    @Override
    public List<String> getSuggestionValue(String userId, String fieldName, String value) {
        List<String> result = new ArrayList<>();
        AbstractAggregationBuilder<TermsAggregationBuilder> suggestionBuilder = AggregationBuilders.terms("suggestion").field("data." + fieldName + ".keyword").size(100);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().size(0).aggregation(suggestionBuilder);
        if (Strings.isNotNullOrEmpty(value)) {
            BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
            queryBuilder.must(QueryBuilders.wildcardQuery("data." + fieldName + ".keyword", "*" + value + "*"));
            searchSourceBuilder.query(queryBuilder);
        }

        SearchRequest searchRequest = new SearchRequest(PREFIX_PERSON_INDEX + userId);
        searchRequest.source(searchSourceBuilder);
        try {
            SearchResponse response = elasticsearchReadAdapter.getClient().search(searchRequest, RequestOptions.DEFAULT);
            Terms suggestionAgg = response.getAggregations().get("suggestion");
            suggestionAgg.getBuckets().forEach(bucket -> result.add(bucket.getKeyAsString()));
        } catch (IOException e) {
            e.printStackTrace();
        }

        return result;
    }

    @Override
    public Set<String> getAllProfileInSegment(String userId, BoolQueryBuilder boolQueryBuilder) {
        Set<String> profileIds = new HashSet<>();
        try {
            RestHighLevelClient client = elasticsearchReadAdapter.getClient();
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQueryBuilder);
            searchSourceBuilder.size(1000);
            SearchRequest searchRequest = new SearchRequest(PREFIX_PERSON_INDEX + userId);
            searchRequest.source(searchSourceBuilder);
            searchRequest.scroll(TimeValue.timeValueMinutes(1L));

            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            String scrollId = response.getScrollId();
            SearchHit[] searchHits = response.getHits().getHits();
            while (searchHits != null && searchHits.length > 0) {
                for (SearchHit hit: searchHits) {
                    String profileId = hit.getId();
                    profileIds.add(profileId);
                }
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(TimeValue.timeValueMinutes(1L));
                response = client.scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollId = response.getScrollId();
                searchHits = response.getHits().getHits();
            }
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return profileIds;
    }

}
