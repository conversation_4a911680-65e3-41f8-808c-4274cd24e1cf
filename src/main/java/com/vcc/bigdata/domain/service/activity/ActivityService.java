package com.vcc.bigdata.domain.service.activity;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ActivityService {

    /**
     * <PERSON><PERSON> hàm đếm số lượng theo loại hoạt động của khách hàng.
     * Thực tế có thể lấy từ nhiều nguồn khác nhau: event, interaction, order, cskh...
     */
    public Map<String, Integer> getSummary(Long profileId) {
        Map<String, Integer> summary = new LinkedHashMap<>();
        summary.put("access_history", 28);       // Số lần truy cập
        summary.put("interactions", 15);         // Số lượt tương tác
        summary.put("cskh_history", 7);          // L<PERSON>ch sử CSKH
        summary.put("orders", 12);               // <PERSON><PERSON> đơn hàng

        return summary;
    }

    /**
     * <PERSON><PERSON><PERSON> danh sách các loại hoạt động khả dụng.
     *
     * @return Danh sách tên các loại hoạt động dạng chuỗi.
     */
    public List<String> getActivityTypes() {
        return Arrays.asList(
                "email",
                "sms",
                "facebook"
        );
    }
}