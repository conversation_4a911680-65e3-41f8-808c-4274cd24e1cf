package com.vcc.bigdata.domain.service.activity;

import com.vcc.bigdata.domain.PagingPayload;
import com.vcc.bigdata.application.dto.activity.OrderDto;
import com.vcc.bigdata.domain.model.activity.OrderItem;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service quản lý đơn hàng giả lập, cung cấp các phương thức
 * để truy vấn danh sách đơn hàng của khách hàng theo các tiêu chí lọc khác nhau cùng phân trang kết quả.
 * <p>
 * Dữ liệu đơn hàng được khởi tạo mock trong bộ nhớ để phục vụ mục đích demo hoặc kiểm thử.
 */
@Service
public class OrderService {

    private final List<OrderDto> mockOrders = new ArrayList<>();

    /**
     * Khởi tạo dữ liệu mock cho 50 đơn hàng với các trạng thái (status),
     * phương thức thanh toán (paymentMethod), sản phẩm kèm số lượng,
     * tổng tiền, và lý do hủy đơn nếu có.
     */
    @PostConstruct
    public void initMockData() {
        List<Status> statuses = new ArrayList<>();
        statuses.add(new Status(1, "Hoàn thành"));
        statuses.add(new Status(2, "Đã hủy"));
        statuses.add(new Status(3, "Đã thanh toán"));
        statuses.add(new Status(4, "Chưa thanh toán"));

        List<PaymentMethod> paymentMethods = new ArrayList<>();
        paymentMethods.add(new PaymentMethod(1, "Thanh toán tiền mặt"));
        paymentMethods.add(new PaymentMethod(2, "Chuyển khoản"));
        paymentMethods.add(new PaymentMethod(3, "Trả góp"));
        paymentMethods.add(new PaymentMethod(4, "Thẻ tín dụng"));

        for (int i = 1; i <= 50; i++) {
            List<OrderItem> items = Collections.singletonList(
                    OrderItem.builder()
                            .productId(String.valueOf(i))
                            .productName("Sản phẩm " + i)
                            .quantity(1 + (i % 3))
                            .build()
            );

            Status status = statuses.get(i % statuses.size());
            PaymentMethod paymentMethod = paymentMethods.get(i % paymentMethods.size());

            OrderDto order = OrderDto.builder()
                    .id(String.valueOf(i))
                    .timestamp(Instant.now().minus(i, ChronoUnit.DAYS))
                    .status(status)
                    .paymentMethod(paymentMethod)
                    .totalAmount(BigDecimal.valueOf(150000 + i * 5000))
                    .products(items.stream().map(item -> OrderDto.ProductSummary.builder()
                            .name(item.getProductName())
                            .quantity(item.getQuantity())
                            .build()).collect(Collectors.toList()))
                    .cancelReason(status.getName().equals("Đã hủy") ? "Khách không nhận hàng" : null)
                    .build();

            mockOrders.add(order);
        }
    }

    /**
     * Lấy danh sách đơn hàng của khách hàng với các bộ lọc theo trạng thái đơn hàng,
     * khoảng thời gian, từ khóa tìm kiếm trong ID hoặc tên sản phẩm, cùng phân trang kết quả.
     *
     * @param profileId  ID khách hàng (hiện không được sử dụng trong dữ liệu mock).
     * @param status      Trạng thái đơn hàng cần lọc (ví dụ: "Hoàn thành", "Đã hủy"),
     *                    nếu null hoặc rỗng thì không lọc theo trạng thái.
     * @param from        Ngày bắt đầu lọc theo định dạng yyyy-MM-dd
     *                    (nếu null hoặc rỗng thì lấy từ thời điểm sớm nhất).
     * @param to          Ngày kết thúc lọc theo định dạng yyyy-MM-dd
     *                    (nếu null hoặc rỗng thì lấy đến thời điểm hiện tại).
     * @param searchQuery Từ khóa tìm kiếm trong ID đơn hàng hoặc tên sản phẩm,
     *                    nếu null hoặc rỗng thì không lọc theo từ khóa.
     * @param offset      Số trang bắt đầu lấy dữ liệu (tối thiểu là 1).
     * @param limit       Số lượng bản ghi tối đa trả về trên mỗi trang (1 đến 100).
     * @return Đối tượng {@link PagingPayload} chứa danh sách đơn hàng thỏa mãn điều kiện,
     * cùng thông tin phân trang (offset, limit, tổng số bản ghi).
     */
    public PagingPayload<OrderDto> getProfileOrders(
            long profileId,
            String status,
            String from ,
            String to,
            String searchQuery,
            int offset,
            int limit
    ) {
        offset = Math.max(offset, 1);
        limit = Math.min(Math.max(limit, 1), 100);

        Instant fromDate = from != null && !from.isEmpty()
                ? LocalDate.parse(from).atStartOfDay(ZoneOffset.UTC).toInstant()
                : Instant.EPOCH;

        Instant toDate = to != null && !to.isEmpty()
                ? LocalDate.parse(to).plusDays(1).atStartOfDay(ZoneOffset.UTC).toInstant()
                : Instant.now();

        Stream<OrderDto> stream = mockOrders.stream()
                .filter(i -> (i.getTimestamp().toEpochMilli() >= fromDate.toEpochMilli())
                        && (i.getTimestamp().toEpochMilli() <= toDate.toEpochMilli()));

        if (searchQuery != null && !searchQuery.isEmpty()) {
            stream = stream.filter(o -> o.getId().contains(searchQuery) || o.getProducts().stream().anyMatch(
                    p -> p.getName().toLowerCase().contains(searchQuery.toLowerCase())
            ));
        }

        if (status != null && !status.isEmpty()) {
            stream = stream.filter(o -> o.getStatus().getName().equalsIgnoreCase(status));
        }

        List<OrderDto> filtered = stream.sorted(Comparator.comparing(OrderDto::getTimestamp).reversed())
                .collect(Collectors.toList());

        int total = filtered.size();
        int fromIndex = Math.max(0, (offset - 1) * limit);
        int toIndex = Math.min(fromIndex + limit, total);
        List<OrderDto> pageList = (fromIndex < total) ? filtered.subList(fromIndex, toIndex) : Collections.emptyList();

        return PagingPayload.<OrderDto>builder()
                .data(pageList)
                .offset(offset)
                .limit(limit)
                .total(total)
                .build();
    }

    /**
     * Lớp mô tả trạng thái đơn hàng với ID và tên trạng thái.
     */
    @Getter
    @AllArgsConstructor
    public static class Status {
        private final int id;
        private final String name;
    }

    /**
     * Lớp mô tả phương thức thanh toán với ID và tên phương thức.
     */
    @Getter
    @AllArgsConstructor
    public static class PaymentMethod {
        private final int id;
        private final String name;
    }
}

