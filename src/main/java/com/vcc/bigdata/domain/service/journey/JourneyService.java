package com.vcc.bigdata.domain.service.journey;

import com.vcc.bigdata.application.dto.journey.EdgeDto;
import com.vcc.bigdata.application.dto.journey.JourneyStepDto;
import com.vcc.bigdata.application.dto.journey.JourneySummaryDto;
import com.vcc.bigdata.presentation.response.journey.JourneyMapResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service mô phỏng hành trình khách hàng (profile journey) bằng dữ liệu giả lập.
 * <p>
 * Cung cấp các bước trong hành trình, định nghĩa luồng di chuyển giữa các bước, và thống kê tổng quan.
 */
@Service
@Slf4j
public class JourneyService {

    private final List<JourneyStepDto> mockEvents = new ArrayList<>();

    /**
     * Khởi tạo dữ liệu log giả lập sau khi service được khởi tạo.
     * <p>
     * Tạo 50 bản ghi log thuộc về 5 khách hàng khác nhau, với các loại hành động như tạo, cập nhật, xóa.
     */
    @PostConstruct
    public void initMockData() {

        JourneyStepDto step1 = JourneyStepDto.builder()
                .stepName("Click Ads")
                .eventType("click")
                .timestamp(Instant.now().minus(5, ChronoUnit.DAYS))
                .metadata(createMetadata("url", "https://ads.example.com", "campaign", "Tết 2025"))
                .build();

        JourneyStepDto step2 = JourneyStepDto.builder()
                .stepName("Landing Page")
                .eventType("view")
                .timestamp(Instant.now().minus(4, ChronoUnit.DAYS))
                .metadata(createMetadata("url", "https://landing.example.com", "channel", "facebook"))
                .build();

        JourneyStepDto step3 = JourneyStepDto.builder()
                .stepName("View Product")
                .eventType("view")
                .timestamp(Instant.now().minus(3, ChronoUnit.DAYS))
                .metadata(createMetadata("product_id", "123", "product_name", "Áo Tết"))
                .build();

        JourneyStepDto step4 = JourneyStepDto.builder()
                .stepName("Fill Form")
                .eventType("submit")
                .timestamp(Instant.now().minus(2, ChronoUnit.DAYS))
                .metadata(createMetadata("form_name", "Đăng ký tư vấn"))
                .build();

        JourneyStepDto step5 = JourneyStepDto.builder()
                .stepName("Thank You Page")
                .eventType("view")
                .timestamp(Instant.now().minus(1, ChronoUnit.DAYS))
                .metadata(createMetadata("message", "Cảm ơn bạn đã đăng ký"))
                .build();

        mockEvents.add(step1);
        mockEvents.add(step2);
        mockEvents.add(step3);
        mockEvents.add(step4);
        mockEvents.add(step5);
    }

    /**
     * Tạo map metadata từ chuỗi key-value.
     *
     * @param keyValues Danh sách các cặp key và value.
     * @return Map chứa các giá trị metadata.
     */
    private Map<String, String> createMetadata(String... keyValues) {
        Map<String, String> metadata = new HashMap<>();
        for (int i = 0; i < keyValues.length - 1; i += 2) {
            metadata.put(keyValues[i], keyValues[i + 1]);
        }
        return metadata;
    }

    /**
     * Lấy hành trình khách hàng dưới dạng các bước và các mối liên hệ giữa bước.
     *
     * @param profileId ID khách hàng.
     * @param from       Ngày bắt đầu lọc hành trình (không sử dụng trong mock).
     * @param to         Ngày kết thúc lọc hành trình (không sử dụng trong mock).
     * @param channel    Kênh tương tác (không sử dụng trong mock).
     * @param campaign   Chiến dịch tương tác (không sử dụng trong mock).
     * @return Đối tượng {@link JourneyMapResponse} chứa danh sách bước và luồng chuyển đổi.
     */
    public JourneyMapResponse getProfileJourney(long profileId, String from, String to, String channel, String campaign) {

        List<JourneyStepDto> sortedSteps = mockEvents.stream()
                .sorted(Comparator.comparing(JourneyStepDto::getTimestamp))
                .collect(Collectors.toList());

        // Build edges: từ step A → B
        List<EdgeDto> edges = new ArrayList<>();
        for (int i = 1; i < sortedSteps.size(); i++) {
            edges.add(EdgeDto.builder()
                    .fromStep(sortedSteps.get(i - 1).getStepName())
                    .toStep(sortedSteps.get(i).getStepName())
                    .build());
        }

        return JourneyMapResponse.builder()
                .steps(sortedSteps)
                .edges(edges)
                .build();
    }

    /**
     * Trả về danh sách các bước định nghĩa sẵn trong hành trình người dùng.
     *
     * @return Danh sách tên các bước.
     */
    public List<String> getStepDefinitions() {
        return Arrays.asList(
                "Click Ads",
                "Landing Page",
                "View Product",
                "Fill Form",
                "Thank You Page",
                "Open Email",
                "Click Email",
                "Receive SMS",
                "Click SMS",
                "Open Push"
        );
    }

    /**
     * Trả về thống kê hành trình khách hàng dưới dạng tổng số bước và các loại tương tác.
     *
     * @param profileId ID khách hàng (chưa được dùng trong mock).
     * @return Đối tượng {@link JourneySummaryDto} chứa thống kê hành trình.
     */
    public JourneySummaryDto getJourneySummary(long profileId) {
        return JourneySummaryDto.builder()
                .totalSteps(5)
                .emailOpens(1)
                .emailClicks(1)
                .smsReceives(1)
                .smsClicks(1)
                .pushOpens(1)
                .conversionRate(0.6)
                .build();
    }

}
