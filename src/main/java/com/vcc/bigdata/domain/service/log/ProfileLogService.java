package com.vcc.bigdata.domain.service.log;

import com.vcc.bigdata.domain.PagingPayload;
import com.vcc.bigdata.application.dto.log.ProfileLogDto;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service cung cấp dữ liệu log hoạt động của khách hàng.
 * <p>
 * Hiện tại sử dụng dữ liệu giả lập (mock) trong bộ nhớ để mô phỏng các hành động như tạo, cập nhật, xóa.
 */
@Service
public class ProfileLogService {

    private final List<ProfileLogDto> mockLogs = new ArrayList<>();

    /**
     * Khởi tạo dữ liệu log giả lập sau khi service được khởi tạo.
     * <p>
     * Tạo 50 bản ghi log thuộc về 5 khách hàng khác nhau, với các loại hành động như tạo, cập nhật, xóa.
     */
    @PostConstruct
    public void initMockData() {
        for (int i = 0; i < 50; i++) {
            mockLogs.add(ProfileLogDto.builder()
                    .id("log_" + i)
                    .profileId("cus_" + (i % 5))  // giả lập 5 khách hàng
                    .actionType(i % 3 == 0 ? "update" : i % 3 == 1 ? "create" : "delete")
                    .content("Thực hiện hành động " + i)
                    .updatedBy("user" + (i % 4) + "@example.com")
                    .timestamp(Instant.now().minus(i, ChronoUnit.HOURS))
                    .ipAddress("10.0.0." + (100 + i))
                    .build());
        }
    }

    /**
     * Lọc và phân trang danh sách log của khách hàng theo các tiêu chí đã cho.
     *
     * @param profileId   ID khách hàng cần lấy log
     * @param from         Ngày bắt đầu lọc (định dạng yyyy-MM-dd), có thể null
     * @param to           Ngày kết thúc lọc (định dạng yyyy-MM-dd), có thể null
     * @param searchQuery  Từ khóa tìm kiếm trong nội dung hoặc người cập nhật log
     * @param type         Loại hành động (create, update, delete), có thể null
     * @param offset       Trang hiện tại (bắt đầu từ 1)
     * @param limit        Số log mỗi trang (giới hạn tối đa 100)
     * @return Danh sách log được phân trang dưới dạng {@link PagingPayload}
     */
    public PagingPayload<ProfileLogDto> getProfileLogs(
            Long profileId,
            String from,
            String to,
            String searchQuery,
            String type,
            int offset,
            int limit
    ) {
        offset = Math.max(offset, 1);
        limit = Math.min(Math.max(limit, 1), 100);

        Instant fromDate = from != null && !from.isEmpty()
                ? LocalDate.parse(from).atStartOfDay(ZoneOffset.UTC).toInstant()
                : Instant.EPOCH;

        Instant toDate = to != null && !to.isEmpty()
                ? LocalDate.parse(to).plusDays(1).atStartOfDay(ZoneOffset.UTC).toInstant()
                : Instant.now();

        // Filter
        Stream<ProfileLogDto> stream = mockLogs.stream()
                .filter(i -> (i.getTimestamp().toEpochMilli() >= fromDate.toEpochMilli())
                        && (i.getTimestamp().toEpochMilli() <= toDate.toEpochMilli()));

        if (searchQuery != null && !searchQuery.isEmpty()) {
            stream = stream.filter(log -> (log.getContent() != null && log.getContent().toLowerCase().contains(searchQuery.toLowerCase()))
                    || (log.getUpdatedBy() != null && log.getUpdatedBy().toLowerCase().contains(searchQuery.toLowerCase())));
        }

        if (type != null && !type.isEmpty()) {
            stream = stream.filter(log -> (log.getActionType().equalsIgnoreCase(type)));
        }

        List<ProfileLogDto> filtered = stream.sorted(Comparator.comparing(ProfileLogDto::getTimestamp).reversed())
                .collect(Collectors.toList());

        int total = filtered.size();
        int fromIndex = Math.max(0, (offset - 1) * limit);
        int toIndex = Math.min(fromIndex + limit, total);

        List<ProfileLogDto> pageList = (fromIndex < total) ? filtered.subList(fromIndex, toIndex) : Collections.emptyList();

        return PagingPayload.<ProfileLogDto>builder()
                .data(pageList)
                .offset(offset)
                .limit(limit)
                .total(total)
                .build();
    }
}
