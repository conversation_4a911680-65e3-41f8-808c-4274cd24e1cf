package com.vcc.bigdata.domain.service.activity;

import com.vcc.bigdata.domain.PagingPayload;
import com.vcc.bigdata.application.dto.activity.EventDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service quản lý dữ liệu sự kiện (Event) gi<PERSON> lập, cung cấp các phương thức để truy vấn
 * danh sách sự kiện với các tùy chọn lọc và phân trang.
 * <p>
 * Dữ liệu sự kiện được khởi tạo mock trong bộ nhớ để phục vụ mục đích demo hoặc kiểm thử.
 */
@Service
@Slf4j
public class EventService {

    private final List<EventDto> mockEvents = new ArrayList<>();

    /**
     * Khởi tạo dữ liệu mock cho danh sách sự kiện gồm 30 sự kiện với timestamp giảm dần,
     * mỗi sự kiện có URL dạng "https://example.com/page/{i}".
     */
    @PostConstruct
    public void initMockData() {
        Instant now = Instant.now();

        for (int i = 0; i < 30; i++) {
            mockEvents.add(new EventDto(
                    String.valueOf(i),
                    now.minus(i, ChronoUnit.DAYS),
                    "https://example.com/page/" + (i + 1)
            ));
        }
    }

    /**
     * Lấy danh sách sự kiện của khách hàng với tùy chọn lọc theo khoảng thời gian, từ khóa tìm kiếm,
     * đồng thời hỗ trợ phân trang.
     *
     * @param profileId  ID khách hàng (hiện không được sử dụng trong mock data).
     * @param limit       Số lượng bản ghi tối đa trả về (giá trị hợp lệ: 1 đến 100).
     * @param offset      Số trang bắt đầu lấy dữ liệu (tối thiểu là 1).
     * @param from        Ngày bắt đầu lọc theo định dạng yyyy-MM-dd (nếu null hoặc rỗng thì lấy từ thời điểm sớm nhất).
     * @param to          Ngày kết thúc lọc theo định dạng yyyy-MM-dd (nếu null hoặc rỗng thì lấy đến thời điểm hiện tại).
     * @param searchQuery Từ khóa tìm kiếm trong URL (nếu null hoặc rỗng thì không lọc theo từ khóa).
     * @return Đối tượng {@link PagingPayload} chứa danh sách sự kiện thỏa mãn điều kiện lọc,
     * cùng thông tin phân trang (limit, offset, tổng số bản ghi).
     */
    public PagingPayload<EventDto> getEvents(
            long profileId,
            int limit,
            int offset,
            String from,
            String to,
            String searchQuery
    ) {
        offset = Math.max(offset, 1);
        limit = Math.min(Math.max(limit, 1), 100);

        Instant fromDate = from != null && !from.isEmpty()
                ? LocalDate.parse(from).atStartOfDay(ZoneOffset.UTC).toInstant()
                : Instant.EPOCH;

        Instant toDate = to != null && !to.isEmpty()
                ? LocalDate.parse(to).plusDays(1).atStartOfDay(ZoneOffset.UTC).toInstant()
                : Instant.now();

        // Filter
        Stream<EventDto> stream = mockEvents.stream()
                .filter(e -> (e.getTimestamp().toEpochMilli() >= fromDate.toEpochMilli())
                                        && (e.getTimestamp().toEpochMilli() <= toDate.toEpochMilli()));

        if (searchQuery != null && !searchQuery.isEmpty()) {
            stream = stream.filter(e -> e.getUrl().toLowerCase().contains(searchQuery.toLowerCase()));
        }

        List<EventDto> filtered = stream.sorted(Comparator.comparing(EventDto::getTimestamp).reversed())
                .collect(Collectors.toList());

        int total = filtered.size();
        int fromIndex = Math.max(0, (offset - 1) * limit);
        int toIndex = Math.min(fromIndex + limit, total);

        List<EventDto> pagedList = (fromIndex < total) ? filtered.subList(fromIndex, toIndex) : Collections.emptyList();

        // Return
        return PagingPayload.<EventDto>builder()
                .data(pagedList)
                .limit(limit)
                .offset(offset)
                .total(total)
                .build();
    }
}

