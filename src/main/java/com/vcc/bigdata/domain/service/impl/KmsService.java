package com.vcc.bigdata.domain.service.impl;

import com.vcc.bigdata.domain.auth.BasicKMSCredentials;
import com.vcc.bigdata.domain.key.models.*;
import com.vcc.bigdata.domain.kms.KMS;
import com.vcc.bigdata.domain.kms.KMSClient;
import com.vcc.bigdata.domain.service.IKmsService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class KmsService implements IKmsService {
    private KMS kms;
    private final String secretKey;
    private Map<String, DescribeDetailKMSKeyResult> clientSecretMap;

    public KmsService(@Value("${kms.domain}") String domain,
                      @Value("${kms.keystore_base64}") String keystoreBase64,
                      @Value("${kms.truststore_base64}") String truststoreBase64,
                      @Value("${kms.key.decrypt_key}") String secretKey,
                      @Value("${kms.password_file}") String passwordFile) {
        this.clientSecretMap = new HashMap<>();
        this.secretKey = secretKey;
        try {
            this.kms = new KMSClient(new BasicKMSCredentials(keystoreBase64, truststoreBase64, passwordFile, domain));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String decrypt(String kmsCustomerId, String data) {
        if (!clientSecretMap.containsKey(kmsCustomerId)) {
            DescribeDetailKMSKeyResult describeDetailKMSKeyResult = kms.describeDetailKMSKey(DescribeDetailKMSKeyRequest.builder().keyId(kmsCustomerId).build());
            clientSecretMap.put(kmsCustomerId, describeDetailKMSKeyResult);
        }

        try {
            DecryptInClientResult decryptInClientResult = kms.decryptInClient(DecryptInClientRequest.builder()
                    .secretKey(clientSecretMap.get(kmsCustomerId).getSecretKey())
                    .input(data)
                    .contentType(ContentType.SINGLE_STRING.getValue())
                    .algorithm(clientSecretMap.get(kmsCustomerId).getAlgorithm())
                    .keyAES(secretKey)
                    .build());
            return decryptInClientResult.getOutput().toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<String> decryptListString(String kmsCustomerId, List<String> data) {
        if (!clientSecretMap.containsKey(kmsCustomerId)) {
            DescribeDetailKMSKeyResult describeDetailKMSKeyResult = kms.describeDetailKMSKey(DescribeDetailKMSKeyRequest.builder().keyId(kmsCustomerId).build());
            clientSecretMap.put(kmsCustomerId, describeDetailKMSKeyResult);
        }

        try {
            DecryptInClientResult decryptInClientResult = kms.decryptInClient(DecryptInClientRequest.builder()
                    .secretKey(clientSecretMap.get(kmsCustomerId).getSecretKey())
                    .input(data)
                    .contentType(ContentType.LIST_STRING.getValue())
                    .algorithm(clientSecretMap.get(kmsCustomerId).getAlgorithm())
                    .keyAES(secretKey)
                    .build());
            return (List<String>) decryptInClientResult.getOutput();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String encrypt(String kmsCustomerId, String data) {
        if (!clientSecretMap.containsKey(kmsCustomerId)) {
            DescribeDetailKMSKeyResult describeDetailKMSKeyResult = kms.describeDetailKMSKey(DescribeDetailKMSKeyRequest.builder().keyId(kmsCustomerId).build());
            clientSecretMap.put(kmsCustomerId, describeDetailKMSKeyResult);
        }

        try {
            EncryptInClientResult encryptInClientResult = kms.encryptInClient(EncryptInClientRequest.builder()
                    .input(data)
                    .contentType(ContentType.SINGLE_STRING.getValue())
                    .secretKey(clientSecretMap.get(kmsCustomerId).getSecretKey())
                    .algorithm(clientSecretMap.get(kmsCustomerId).getAlgorithm())
                    .keyAES(secretKey)
                    .build());
            return encryptInClientResult.getOutput().toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
