package com.vcc.bigdata.domain.service.activity;

import com.vcc.bigdata.domain.PagingPayload;
import com.vcc.bigdata.application.dto.activity.CSKHActivityDto;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service quản lý lịch sử hoạt động chăm sóc khách hàng (CSKH).
 * <p>
 * Cung cấp các phương thức truy vấn danh sách các hoạt động CSKH đã thực hiện,
 * hỗ trợ lọc theo khoảng thời gian, từ khóa tìm kiếm và phân trang kết quả.
 * <p>
 * Dữ liệu được khởi tạo mock trong bộ nhớ để mô phỏng các hoạt động CSKH.
 */
@Service
public class CSKHActivityService {

    private final List<CSKHActivityDto> mockActivities = new ArrayList<>();

    /**
     * Khởi tạo dữ liệu mock cho 50 hoạt động CSKH với các thông tin:
     * - ID hoạt động
     * - Thời gian thực hiện
     * - Loại hành động (gọi điện, xác nhận đơn, tư vấn)
     * - Ghi chú chi tiết
     * - Email người xử lý
     * - ID phiếu hỗ trợ (ticket) nếu có
     */
    @PostConstruct
    public void initMockData() {
        for (int i = 1; i <= 50; i++) {
            CSKHActivityDto activity = CSKHActivityDto.builder()
                    .id(String.valueOf(i))
                    .timestamp(Instant.now().minus(i, ChronoUnit.DAYS))
                    .action(i % 3 == 0 ? "Gọi điện" : (i % 3 == 1 ? "Xác nhận đơn" : "Tư vấn"))
                    .note("Ghi chú hoạt động " + i)
                    .handlerEmail("agent" + (i % 5) + "@support.vn")
                    .ticketId(i % 4 == 0 ? 1000L + i : null)
                    .build();
            mockActivities.add(activity);
        }
    }

    /**
     * Lấy lịch sử hoạt động CSKH của khách hàng với các bộ lọc theo khoảng thời gian,
     * từ khóa tìm kiếm trong hành động hoặc ghi chú, cùng phân trang kết quả.
     *
     * @param profileId  ID khách hàng (hiện không được sử dụng trong dữ liệu mock).
     * @param from        Ngày bắt đầu lọc theo định dạng yyyy-MM-dd
     *                    (nếu null hoặc rỗng thì lấy từ thời điểm sớm nhất).
     * @param to          Ngày kết thúc lọc theo định dạng yyyy-MM-dd
     *                    (nếu null hoặc rỗng thì lấy đến thời điểm hiện tại).
     * @param searchQuery Từ khóa tìm kiếm trong hành động hoặc ghi chú,
     *                    nếu null hoặc rỗng thì không lọc theo từ khóa.
     * @param offset      Số trang bắt đầu lấy dữ liệu (tối thiểu là 1).
     * @param limit       Số lượng bản ghi tối đa trả về trên mỗi trang (1 đến 100).
     * @return Đối tượng {@link PagingPayload} chứa danh sách hoạt động CSKH thỏa mãn điều kiện,
     * cùng thông tin phân trang (offset, limit, tổng số bản ghi).
     */
    public PagingPayload<CSKHActivityDto> getCSKHHistory(
            Long profileId,
            String from,
            String to,
            String searchQuery,
            int offset,
            int limit
    ) {
        offset = Math.max(offset, 1);
        limit = Math.min(Math.max(limit, 1), 100);

        Instant fromDate = from != null && !from.isEmpty()
                ? LocalDate.parse(from).atStartOfDay(ZoneOffset.UTC).toInstant()
                : Instant.EPOCH;

        Instant toDate = to != null && !to.isEmpty()
                ? LocalDate.parse(to).plusDays(1).atStartOfDay(ZoneOffset.UTC).toInstant()
                : Instant.now();

        // Filter
        Stream<CSKHActivityDto> stream = mockActivities.stream()
                .filter(i -> (i.getTimestamp().toEpochMilli() >= fromDate.toEpochMilli())
                        && (i.getTimestamp().toEpochMilli() <= toDate.toEpochMilli()));

        if (searchQuery != null && !searchQuery.isEmpty()) {
            stream = stream.filter(cskh -> cskh.getAction().toLowerCase().contains(searchQuery.toLowerCase())
                                                        || cskh.getNote().toLowerCase().contains(searchQuery.toLowerCase()));
        }

        List<CSKHActivityDto> filtered = stream.sorted(Comparator.comparing(CSKHActivityDto::getTimestamp).reversed())
                .collect(Collectors.toList());

        int total = filtered.size();
        int fromIndex = Math.max(0, (offset - 1) * limit);
        int toIndex = Math.min(fromIndex + limit, total);

        List<CSKHActivityDto> pageList = (fromIndex < total) ? filtered.subList(fromIndex, toIndex) : Collections.emptyList();

        return PagingPayload.<CSKHActivityDto>builder()
                .data(pageList)
                .offset(offset)
                .limit(limit)
                .total(total)
                .build();
    }
}
