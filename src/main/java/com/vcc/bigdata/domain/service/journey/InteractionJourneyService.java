package com.vcc.bigdata.domain.service.journey;

import com.vcc.bigdata.domain.PagingPayload;
import com.vcc.bigdata.application.dto.journey.InteractionJourneyDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service dùng để mô phỏng và truy vấn các tương tác của khách hàng qua các kênh như email, SMS, và push notification.
 * <p>
 * Dữ liệu trong service này được khởi tạo bằng mock, nhằm phục vụ việc demo hoặc kiểm thử logic hiển thị hành trình tương tác.
 */
@Service
@Slf4j
public class InteractionJourneyService {

    private final Map<String, List<InteractionJourneyDto>> mockData = new HashMap<>();

    /**
     * Khởi tạo dữ liệu giả lập cho các kênh tương tác: email, sms và push notification.
     * <p>
     * Mỗi kênh sẽ chứa danh sách các tương tác mô phỏng, có thông tin như loại sự kiện, chiến dịch, địa chỉ, thời gian, v.v.
     */
    @PostConstruct
    public void initMockData() {

        mockData.put("email", Arrays.asList(
                createInteraction("Email_1", "Open", "Tết 2025", "<EMAIL>", "<EMAIL>", "CampaignMailer", "https://ads.example.com", Instant.now().minus(3, ChronoUnit.DAYS)),
                createInteraction("Email_2", "Click", "Tết 2025", "<EMAIL>", "<EMAIL>", "CampaignMailer", "https://product.example.com", Instant.now().minus(2, ChronoUnit.DAYS))
        ));

        mockData.put("sms", Arrays.asList(
                createInteraction("SMS_1", "Deliver", "Xuân Sale", "SMSGateway", "0123456789", "SMSVendor", "https://deliver.example.com", Instant.now().minus(4, ChronoUnit.DAYS)),
                createInteraction("SMS_2", "Click", "Xuân Sale", "SMSGateway", "0123456789", "SMSVendor", "https://sale.example.com", Instant.now().minus(3, ChronoUnit.DAYS))
        ));

        mockData.put("push", Arrays.asList(
                createInteraction("Push_1", "Open", "Flash Deal", "PushService", "userDevice123", "PushPlatform", "https://app.example.com", Instant.now().minus(1, ChronoUnit.DAYS))
        ));
    }

    /**
     * Tạo một đối tượng tương tác {@link InteractionJourneyDto} từ các trường thông tin đầu vào.
     *
     * @param id           ID sự kiện.
     * @param eventType    Loại sự kiện (Open, Click, Deliver, ...).
     * @param campaignName Tên chiến dịch.
     * @param from         Địa chỉ người gửi (email, số điện thoại, ...).
     * @param to           Địa chỉ người nhận (email, số điện thoại, thiết bị, ...).
     * @param sender       Đơn vị gửi.
     * @param url          URL đính kèm với tương tác.
     * @param timestamp    Thời điểm xảy ra tương tác.
     * @return Đối tượng {@link InteractionJourneyDto} đã được khởi tạo.
     */
    private InteractionJourneyDto createInteraction(String id, String eventType, String campaignName, String from, String to,
                                             String sender, String url, Instant timestamp) {
        return InteractionJourneyDto.builder()
                .id(id)
                .eventType(eventType)
                .campaignName(campaignName)
                .fromEmail(from)
                .toEmail(to)
                .sender(sender)
                .url(url)
                .timestamp(timestamp)
                .build();
    }

    /**
     * Lấy danh sách tương tác của khách hàng theo từng kênh (email, sms, push), hỗ trợ lọc và phân trang.
     *
     * @param channel     Kênh tương tác (email, sms, push).
     * @param profileId  ID khách hàng (chưa sử dụng trong mock).
     * @param from        Ngày bắt đầu lọc (định dạng yyyy-MM-dd).
     * @param to          Ngày kết thúc lọc (định dạng yyyy-MM-dd).
     * @param searchQuery Từ khóa tìm kiếm theo tên chiến dịch hoặc URL.
     * @param campaignId  ID chiến dịch (chưa dùng trong mock).
     * @param eventType   Loại sự kiện cần lọc (Open, Click, ...).
     * @param offset      Trang bắt đầu (>=1).
     * @param limit       Số lượng bản ghi/trang (1–100).
     * @return Danh sách kết quả dạng phân trang {@link PagingPayload} chứa {@link InteractionJourneyDto}.
     */
    public PagingPayload<InteractionJourneyDto> getInteractions(
            String channel,
            String profileId,
            String from,
            String to,
            String searchQuery,
            String campaignId,
            String eventType,
            int offset,
            int limit
    ) {
        offset = Math.max(offset, 1);
        limit = Math.min(Math.max(limit, 1), 100);

        List<InteractionJourneyDto> interactions = mockData.getOrDefault(channel, Collections.emptyList());

        Instant fromDate = from != null && !from.isEmpty()
                ? LocalDate.parse(from).atStartOfDay(ZoneOffset.UTC).toInstant()
                : Instant.EPOCH;

        Instant toDate = to != null && !to.isEmpty()
                ? LocalDate.parse(to).plusDays(1).atStartOfDay(ZoneOffset.UTC).toInstant()
                : Instant.now();

        // Filter
        Stream<InteractionJourneyDto> stream = interactions.stream()
                .filter(i -> (i.getTimestamp().toEpochMilli() >= fromDate.toEpochMilli())
                        && (i.getTimestamp().toEpochMilli() <= toDate.toEpochMilli()));

        if (searchQuery != null && !searchQuery.isEmpty()) {
            stream = stream.filter(i -> i.getCampaignName().toLowerCase().contains(searchQuery.toLowerCase())
                    || i.getUrl().toLowerCase().contains(searchQuery.toLowerCase()));
        }

        if (campaignId != null && !campaignId.isEmpty()) {
            // filter campaign by id
        }

        if (eventType != null && !eventType.isEmpty()) {
            stream = stream.filter(i -> i.getEventType().equalsIgnoreCase(eventType));
        }

        List<InteractionJourneyDto> filtered = stream.sorted(Comparator.comparing(InteractionJourneyDto::getTimestamp).reversed())
                .collect(Collectors.toList());

        int total = filtered.size();
        int fromIndex = Math.max(0, (offset - 1) * limit);
        int toIndex = Math.min(fromIndex + limit, total);

        List<InteractionJourneyDto> pageList = (fromIndex < total) ? filtered.subList(fromIndex, toIndex) : Collections.emptyList();

        return PagingPayload.<InteractionJourneyDto>builder()
                .data(pageList)
                .offset(offset)
                .limit(limit)
                .total(total)
                .build();
    }
}
