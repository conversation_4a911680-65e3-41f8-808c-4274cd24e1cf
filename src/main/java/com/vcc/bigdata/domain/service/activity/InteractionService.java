package com.vcc.bigdata.domain.service.activity;

import com.vcc.bigdata.domain.PagingPayload;
import com.vcc.bigdata.application.dto.activity.InteractionDto;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service quản lý dữ liệu tương tác (Interaction) gi<PERSON> lập, cung cấp các phương thức
 * để truy vấn danh sách tương tác theo các tiêu chí lọc khác nhau cùng phân trang kết quả.
 * <p>
 * Dữ liệu tương tác được khởi tạo mock trong bộ nhớ để phục vụ mục đích demo hoặc kiểm thử.
 */
@Service
public class InteractionService {

    private final List<InteractionDto> mockInteractions = new ArrayList<>();

    /**
     * Khởi tạo dữ liệu mock cho danh sách 50 tương tác, với kiểu interactionType tuần tự
     * xen kẽ giữa "email", "sms" và "facebook", cùng các nội dung hoặc link kèm metadata.
     */
    @PostConstruct
    public void initMockData() {
        for (int i = 0; i < 50; i++) {
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("campaign", "Chiến dịch " + i);
            mockInteractions.add(InteractionDto.builder()
                    .id(String.valueOf(i))
                    .timestamp(Instant.now().minus(i, ChronoUnit.DAYS))
                    .interactionType(i % 3 == 0 ? "email" : i % 3 == 1 ? "sms" : "facebook")
                    .content(i % 2 == 0 ? "Nội dung tương tác số " + i : null)
                    .link(i % 2 != 0 ? "https://example.com/link-" + i : null)
                    .extraMetadata(metadata)
                    .build());
        }
    }

    /**
     * Lấy danh sách tương tác của khách hàng với các bộ lọc theo khoảng thời gian,
     * loại tương tác, từ khóa tìm kiếm trong nội dung hoặc link, cùng phân trang.
     *
     * @param profileId  ID khách hàng (hiện không được sử dụng trong dữ liệu mock).
     * @param from        Ngày bắt đầu lọc theo định dạng yyyy-MM-dd
     *                    (nếu null hoặc rỗng thì lấy từ thời điểm sớm nhất).
     * @param to          Ngày kết thúc lọc theo định dạng yyyy-MM-dd
     *                    (nếu null hoặc rỗng thì lấy đến thời điểm hiện tại).
     * @param type        Loại tương tác (ví dụ: "email", "sms", "facebook"),
     *                    nếu null hoặc rỗng thì không lọc theo loại.
     * @param searchQuery Từ khóa tìm kiếm trong nội dung hoặc link tương tác,
     *                    nếu null hoặc rỗng thì không lọc theo từ khóa.
     * @param offset      Số trang bắt đầu lấy dữ liệu (tối thiểu là 1).
     * @param limit       Số lượng bản ghi tối đa trả về trên mỗi trang (1 đến 100).
     * @return Đối tượng {@link PagingPayload} chứa danh sách tương tác thỏa mãn điều kiện,
     * cùng thông tin phân trang (offset, limit, tổng số bản ghi).
     */
    public PagingPayload<InteractionDto> getInteractions(
            long profileId,
            String from,
            String to,
            String type,
            String searchQuery,
            int offset,
            int limit
    ) {
        offset = Math.max(offset, 1);
        limit = Math.min(Math.max(limit, 1), 100);

        Instant fromDate = from != null && !from.isEmpty()
                ? LocalDate.parse(from).atStartOfDay(ZoneOffset.UTC).toInstant()
                : Instant.EPOCH;

        Instant toDate = to != null && !to.isEmpty()
                ? LocalDate.parse(to).plusDays(1).atStartOfDay(ZoneOffset.UTC).toInstant()
                : Instant.now();

        // Filter
        Stream<InteractionDto> stream = mockInteractions.stream()
                .filter(i -> (i.getTimestamp().toEpochMilli() >= fromDate.toEpochMilli())
                        && (i.getTimestamp().toEpochMilli() <= toDate.toEpochMilli()));

        if (searchQuery != null && !searchQuery.isEmpty()) {
            stream = stream.filter(i -> i.getContent() != null ? i.getContent().toLowerCase().contains(searchQuery.toLowerCase())
                                                    : i.getLink().toLowerCase().contains(searchQuery.toLowerCase()));
        }

        if (type != null && !type.isEmpty()) {
            stream = stream.filter(i -> i.getInteractionType().equalsIgnoreCase(type));
        }

        List<InteractionDto> filtered = stream.sorted(Comparator.comparing(InteractionDto::getTimestamp).reversed())
                .collect(Collectors.toList());

        int total = filtered.size();
        int fromIndex = Math.max(0, (offset - 1) * limit);
        int toIndex = Math.min(fromIndex + limit, total);

        List<InteractionDto> pageList = (fromIndex < total) ? filtered.subList(fromIndex, toIndex) : Collections.emptyList();

        return PagingPayload.<InteractionDto>builder()
                .data(pageList)
                .offset(offset)
                .limit(limit)
                .total(total)
                .build();
    }
}

