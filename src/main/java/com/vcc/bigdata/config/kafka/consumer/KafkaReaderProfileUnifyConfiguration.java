package com.vcc.bigdata.config.kafka.consumer;

import com.vcc.bigdata.config.properties.KafkaConsumerProperties;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Configuration
public class KafkaReaderProfileUnifyConfiguration {

    @Value("${kafka.consumer.unify.profile.username}")
    private String username;
    @Value("${kafka.consumer.unify.profile.password}")
    private String password;
    @Value("${kafka.consumer.unify.profile.security-protocol}")
    private String securityProtocol;
    @Value("${kafka.consumer.unify.profile.sasl-mechanism}")
    private String saslMechanism;
    @Value("${kafka.consumer.unify.profile.sasl-jaas-config}")
    private String saslJaasConfig;

    @Bean("kafkaConsumerUnifyProfileProperties")
    @ConfigurationProperties(prefix = "kafka.consumer.unify.profile")
    public KafkaConsumerProperties kafkaConsumerProperties() {
        return new KafkaConsumerProperties();
    }

    @Bean("kafkaBrokerReaderUnifyProfileProperties")
    public Properties kafkaBrokerReaderProperties() {
        final Properties properties = new Properties();
        KafkaConsumerProperties consumerProperties = kafkaConsumerProperties();
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, consumerProperties.getBootstrapServers());
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, consumerProperties.getGroupId());
        properties.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, consumerProperties.getMaxPollRecords());
        properties.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, consumerProperties.getEnableAutoCommit());
        properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, consumerProperties.getAutoOffsetReset());
        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, consumerProperties.getKeyDeserializer());
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, consumerProperties.getValueDeserializer());
        properties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, securityProtocol);
        properties.put(SaslConfigs.SASL_MECHANISM, saslMechanism);
        properties.put(SaslConfigs.SASL_JAAS_CONFIG, String.format(saslJaasConfig, username, password));
        return properties;
    }
}
