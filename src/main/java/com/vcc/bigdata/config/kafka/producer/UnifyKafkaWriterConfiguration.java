package com.vcc.bigdata.config.kafka.producer;

import com.vcc.bigdata.config.properties.KafkaProducerProperties;
import com.vcc.bigdata.infrastructure.impl.write.KafkaBrokerWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Slf4j
@Configuration
public class UnifyKafkaWriterConfiguration {
    @Value("${unify.kafka.producer.username}")
    private String username;

    @Value("${unify.kafka.producer.password}")
    private String password;

    @Value("${unify.kafka.producer.security-protocol}")
    private String securityProtocol;

    @Value("${unify.kafka.producer.sasl-mechanism}")
    private String saslMechanism;

    @Value("${unify.kafka.producer.sasl-jaas-config}")
    private String saslJaasConfig;

    @Bean("unifyKafkaProducerProperties")
    @ConfigurationProperties(prefix = "unify.kafka.producer")
    public KafkaProducerProperties unifyKafkaProducerProperties() {
        return new KafkaProducerProperties();
    }

    @Bean("unifyKafkaBrokerWriterProperties")
    public Properties kafkaBrokerWriterProperties() {
        final Properties properties = new Properties();
        KafkaProducerProperties producerProperties = unifyKafkaProducerProperties();
        properties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, producerProperties.getBootstrapServers());
        properties.put(ProducerConfig.ACKS_CONFIG, "all");
        properties.put(ProducerConfig.RETRIES_CONFIG, 3);
        properties.put(ProducerConfig.LINGER_MS_CONFIG, 10);
        properties.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        properties.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);
        properties.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, 5242880);
        properties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, producerProperties.getKeySerializer());
        properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, producerProperties.getValueSerializer());
        properties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, securityProtocol);
        properties.put(SaslConfigs.SASL_MECHANISM, saslMechanism);
        properties.put(SaslConfigs.SASL_JAAS_CONFIG, String.format(saslJaasConfig, username, password));
        return properties;
    }

    @Bean("unifyKafkaBrokerWriter")
    public KafkaBrokerWriter unifyKafkaBrokerWriter(@Qualifier("unifyKafkaBrokerWriterProperties") Properties configWriter,
                                                  @Value("${unify.kafka.topic}") String topicName) {
        return new KafkaBrokerWriter(configWriter, topicName);
    }
}
