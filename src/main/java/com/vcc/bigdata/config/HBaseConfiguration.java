package com.vcc.bigdata.config;

import com.vcc.bigdata.domain.model.*;
import com.vcc.bigdata.infrastructure.impl.read.HBaseReadAdapter;
import com.vcc.bigdata.infrastructure.impl.write.HBaseWriteAdapter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
public class HBaseConfiguration {
    @Value("${hbase.zookeeper.quorum}")
    private String HOSTS;

    @Value("${hbase.zookeeper.property.clientPort}")
    private int PORT;

    @Value("${hbase.zookeeper.znode.parent}")
    private String PATH;

    @Value("${hbase.namespace}")
    private String NAMESPACE;

    @Bean("hbasePersonWriteAdapter")
    public HBaseWriteAdapter<Person> personHBaseWriteAdapter() throws IOException {
        return new HBaseWriteAdapter<Person>(HOSTS, PORT, PATH, NAMESPACE) {
            @Override
            protected byte[] buildRowKey(Person object) {
                return buildCommonRowKey(object.getProfileId(), object.getProfileId());
            }
        };
    }

    @Bean("hbasePersonReadAdapter")
    public HBaseReadAdapter<Person> personHBaseReadAdapter() throws IOException {
        return new HBaseReadAdapter<Person>(HOSTS, PORT, PATH, NAMESPACE) {
            @Override
            protected byte[] buildRowKey(Person object) {
                return new byte[0];
            }
        };
    }

    @Bean("hbaseBaseWriteAdapter")
    public HBaseWriteAdapter<String> baseHBaseWriteAdapter() throws IOException {
        return new HBaseWriteAdapter<String>(HOSTS, PORT, PATH, NAMESPACE) {
            @Override
            protected byte[] buildRowKey(String rowkey) {
                return buildCommonRowKey(rowkey, rowkey);
            }
        };
    }

    @Bean("hbaseBaseReadAdapter")
    public HBaseReadAdapter<String> baseHBaseReadAdapter() throws IOException {
        return new HBaseReadAdapter<String>(HOSTS, PORT, PATH, NAMESPACE) {
            @Override
            protected byte[] buildRowKey(String rowkey) {
                return new byte[0];
            }
        };
    }

}
