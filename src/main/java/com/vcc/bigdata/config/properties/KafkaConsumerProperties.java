package com.vcc.bigdata.config.properties;

import lombok.Data;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;

@Data
public class KafkaConsumerProperties {
    private String bootstrapServers;

    private String groupId;

    private String enableAutoCommit = "false";

    private String autoOffsetReset = "earliest";

    private Integer maxPollRecords = 100;

    private String keyDeserializer = StringDeserializer.class.getName();

    private String valueDeserializer = ByteArrayDeserializer.class.getName();
}
