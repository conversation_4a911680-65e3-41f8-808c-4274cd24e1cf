package com.vcc.bigdata.config.properties;

import lombok.Data;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;

@Data
public class KafkaProducerProperties {
    private String bootstrapServers;

    private String keySerializer = StringSerializer.class.getName();

    private String valueSerializer = ByteArraySerializer.class.getName();
}
