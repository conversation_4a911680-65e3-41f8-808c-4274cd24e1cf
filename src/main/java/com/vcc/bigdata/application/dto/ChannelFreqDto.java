package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.*;

import java.sql.Timestamp;
import java.util.List;

@AllArgsConstructor
@Data
@Builder
@NoArgsConstructor
public class ChannelFreqDto {
    @JsonProperty("profile_id")
    private String profileId;
    @JsonProperty("channel_freq")
    private List<ChannelFreqInfo> channelFreqInfos;
    @JsonProperty("last_updated_at")
    private Timestamp lastUpdatedAt;

    @AllArgsConstructor
    @Getter
    @Builder
    @NoArgsConstructor
    public static class ChannelFreqInfo {
        private ChannelConnectType channel;
        private Integer freq;
    }
    public enum ChannelConnectType {
        MOBILE, WEBSITE, APP, EMAIL, OTHER;
        @JsonValue
        public String toLowerCase() {
            return this.name().toLowerCase();
        }
    }
}
