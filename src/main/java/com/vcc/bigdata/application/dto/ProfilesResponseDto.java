package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.vcc.bigdata.domain.model.Person;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProfilesResponseDto {
    @JsonProperty("profiles")
    private List<Person> profiles;

    @JsonProperty("schema")
    private List<String> schema;
}
