package com.vcc.bigdata.application.dto.journey;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JourneyStepDto {
    @JsonProperty("step_name")
    private String stepName;
    @JsonProperty("event_type")
    private String eventType;
    @JsonProperty("timestamp")
    private Instant timestamp;
    @JsonProperty("metadata")
    private Map<String, String> metadata;
}
