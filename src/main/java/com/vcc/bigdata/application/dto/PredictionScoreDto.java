package com.vcc.bigdata.application.dto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.*;

import java.sql.Timestamp;

@AllArgsConstructor
@Getter
@Builder
@NoArgsConstructor
public class PredictionScoreDto {
    @JsonProperty("profile_id")
    private String profileId;

    @JsonProperty("high_ltv")
    private Double highLtv;

    @JsonProperty("recommends")
    private Double recommends;

    @JsonProperty("purchase_frequently")
    private Double purchaseFrequently;

    @JsonProperty("convenience_shopper")
    private Double convenienceShopper;

    @JsonProperty("churn")
    private Double churn;

    @JsonProperty("last_updated_at")
    private Timestamp lastUpdatedAt;
}
