package com.vcc.bigdata.application.dto.journey;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EdgeDto {
    @JsonProperty("from_step")
    private String fromStep;
    @JsonProperty("to_step")
    private String toStep;
}
