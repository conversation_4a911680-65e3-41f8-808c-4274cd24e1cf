package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
@AllArgsConstructor
@Getter
@Builder
@NoArgsConstructor
public class ConnectionDto {
    @JsonProperty("profile_id")
    private String profileId;
    private Map<ChannelConnectType, List<String>> channels;

    public enum ChannelConnectType {
        FACEBOOK,
        ZALO,
        EMAIL,
        PHONE;
        @JsonValue
        public String toLowerCase() {
            return this.name().toLowerCase();
        }
    }
}
