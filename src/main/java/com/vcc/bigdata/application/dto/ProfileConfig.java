package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@AllArgsConstructor
@Getter
@NoArgsConstructor
@Builder
public class ProfileConfig {
    @JsonProperty("profile_id")
    private String profileId;
    @JsonProperty("list_chart")
    private List<Integer> listChart;

    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    @Builder
    public static class ProfileConfigInfo {
        @JsonProperty("profile_id")
        private String profileId;
        @JsonProperty("chart_id")
        private int chartId;
    }
}
