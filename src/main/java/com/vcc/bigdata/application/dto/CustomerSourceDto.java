package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@AllArgsConstructor
@Getter
@Builder
@NoArgsConstructor
public class CustomerSourceDto {
    @JsonProperty("profile_id")
    private String customerId;
    @JsonProperty("source_id")
    private String sourceId;
    @JsonProperty("source_name")
    private String sourceName;
    @JsonProperty("source_type")
    private String sourceType;
    @JsonProperty("last_updated_at")
    private Timestamp lastUpdatedAt;
}
