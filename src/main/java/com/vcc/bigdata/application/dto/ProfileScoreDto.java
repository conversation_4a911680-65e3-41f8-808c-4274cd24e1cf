package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@AllArgsConstructor
@Getter
@NoArgsConstructor
@Builder
public class ProfileScoreDto {
    @JsonProperty("profile_id")
    private String profileId;

    @JsonProperty("lead_score")
    private Double leadScore;

    @JsonProperty("clv_score")
    private Long clvScore;

    @JsonProperty("data_quality_score")
    private Double dataQualityScore;

    @JsonProperty("engagement_score")
    private Double engagementScore;

    @JsonProperty("last_updated_at")
    private Timestamp lastUpdatedAt;
}
