package com.vcc.bigdata.application.dto.journey;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JourneySummaryDto {
    @JsonProperty("total_steps")
    private int totalSteps;
    @JsonProperty("email_opens")
    private int emailOpens;
    @JsonProperty("email_clicks")
    private int emailClicks;
    @JsonProperty("sms_receives")
    private int smsReceives;
    @JsonProperty("sms_clicks")
    private int smsClicks;
    @JsonProperty("push_opens")
    private int pushOpens;
    @JsonProperty("conversion_rate")
    private double conversionRate;
}
