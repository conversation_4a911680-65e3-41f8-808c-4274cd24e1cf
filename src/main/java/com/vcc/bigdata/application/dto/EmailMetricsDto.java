package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.sql.Timestamp;

@AllArgsConstructor
@Data
@Builder
@NoArgsConstructor
public class EmailMetricsDto {
    @JsonProperty("profile_id")
    private String profileId;
    @JsonProperty("delivered")
    private Integer delivered;

    /**
     * open rate example 0.13
     */
    @JsonProperty("open_rate")
    private Double openRate;

    /**
     * click rate example 0.13
     */
    @JsonProperty("click_rate")
    private Double clickRate;
    /**
     * conversion rate example 0.13
     */
    @JsonProperty("conversion_rate")
        private Double conversionRate;
    @JsonProperty("last_updated_at")
    private Timestamp lastUpdatedAt;
}
