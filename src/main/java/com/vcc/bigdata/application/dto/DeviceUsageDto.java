package com.vcc.bigdata.application.dto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.*;

import java.sql.Timestamp;
import java.util.List;

@AllArgsConstructor
@Getter
@Builder
@NoArgsConstructor
public class DeviceUsageDto {
    @JsonProperty("profile_id")
    private String profileId;
    @JsonProperty("device_usages")
    private List<DeviceUsageInfo> deviceUsages;

    @JsonProperty("last_updated_at")
    private Timestamp lastUpdatedAt;

    @AllArgsConstructor
    @Getter
    @NoArgsConstructor
    @Builder
    public static class DeviceUsageInfo {
        @JsonProperty("device_type")
        private DeviceType deviceType;
        @JsonProperty("last_active_at")
        private Timestamp lastActiveAt;
    }

    public enum DeviceType {
        MOBILE, DESKTOP, TABLET;
        @JsonValue
        public String toLowerCase() {
            return this.name().toLowerCase();
        }
    }
}
