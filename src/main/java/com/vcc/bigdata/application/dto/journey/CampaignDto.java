package com.vcc.bigdata.application.dto.journey;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CampaignDto {

    @JsonProperty("id")
    private String id;

    @JsonProperty("name")
    private String name;
}
