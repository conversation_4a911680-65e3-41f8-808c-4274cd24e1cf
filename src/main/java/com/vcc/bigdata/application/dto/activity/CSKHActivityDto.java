package com.vcc.bigdata.application.dto.activity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CSKHActivityDto {

    @JsonProperty("id")
    private String id;

    @JsonProperty("timestamp")
    private Instant timestamp;

    @JsonProperty("action")
    private String action;

    @JsonProperty("note")
    private String note;

    @JsonProperty("handler_email")
    private String handlerEmail;

    @JsonProperty("ticket_id")
    private Long ticketId;
}
