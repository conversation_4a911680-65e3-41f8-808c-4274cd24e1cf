package com.vcc.bigdata.application.dto.activity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.vcc.bigdata.domain.service.activity.OrderService;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderDto {

    @JsonProperty("id")
    private String id;

    @JsonProperty("order_id")
    private String orderId;

    @JsonProperty("products")
    private List<ProductSummary> products;

    @JsonProperty("total_amount")
    private BigDecimal totalAmount;

    @JsonProperty("payment_method")
    private OrderService.PaymentMethod paymentMethod;

    @JsonProperty("status")
    private OrderService.Status status;

    @JsonProperty("timestamp")
    private Instant timestamp;

    @JsonProperty("cancel_reason")
    private String cancelReason;

    @Data
    @Builder
    public static class ProductSummary {
        private String name;
        private int quantity;
    }
}
