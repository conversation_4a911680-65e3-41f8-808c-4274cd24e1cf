package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfileManagementDto {
    private String id;

    private String name;

    @JsonProperty("user_id")
    private String userId;

    private Integer status;

    private List<Mapping> mapping;

    private List<SourceInfo> sources;

    @JsonProperty("create_time")
    private Timestamp createTime;

    @JsonProperty("update_time")
    private Timestamp updateTime;

    @JsonProperty("kms_customer_id")
    private String kmsCustomerId;

    // external info
    @JsonProperty("total_record")
    private Integer totalRecord;

    @JsonProperty("duplicate_profile")
    private Integer duplicateProfile;

    @JsonProperty("unified_profile")
    private Integer unifiedProfile;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Mapping {
        @JsonProperty("field_mapping")
        private String fieldMapping;

        @JsonProperty("field_name")
        private String fieldName;

        @JsonProperty("data_type")
        private String dataType;

        private String demographic;

        @JsonProperty("masking_type")
        private Integer maskingType;

        @JsonProperty("sub_fields")
        private Map<String, Mapping> subFields;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SourceInfo {
        private String id;

        private Integer type;

        @JsonProperty("connector_type")
        private Integer connectorType;

        // external info
        private String name;

        @JsonProperty("connector_logo")
        private String connectorLogo;
    }
}