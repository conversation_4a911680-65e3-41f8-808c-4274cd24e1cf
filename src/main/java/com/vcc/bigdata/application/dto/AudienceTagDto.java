package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Builder
public class AudienceTagDto {
    @JsonProperty("profile_id")
    private String profileId;
    @JsonProperty("audience_id")
    private String audienceId;
    @JsonProperty("audience_studio")
    private String audienceStudio;
    @JsonProperty("last_updated_at")
    private Timestamp lastUpdatedAt;
}
