package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.sql.Timestamp;
import java.util.List;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProfileInfoDto {
    @JsonProperty("profile_id")
    @Setter
    private String profileId;
    private String name;
    private List<String> email;
    private List<String> phones;
    private String address;
    @JsonProperty("marriage_status")
    private String marriageStatus;
    private int gender;
    private String job;
    @JsonProperty("create_time")
    private Timestamp createTime;
    @JsonProperty("update_time")
    private Timestamp updateTime;
}
