package com.vcc.bigdata.application.dto.activity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.Map;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InteractionDto {

    @JsonProperty("id")
    private String id;

    @JsonProperty("timestamp")
    private Instant timestamp;

    @JsonProperty("interaction_type")
    private String interactionType;

    @JsonProperty("content")
    private String content;

    @JsonProperty("link")
    private String link;

    @JsonProperty("extra_metadata")
    private Map<String, Object> extraMetadata;
}