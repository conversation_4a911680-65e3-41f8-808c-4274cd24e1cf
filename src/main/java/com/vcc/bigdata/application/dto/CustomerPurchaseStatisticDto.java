package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Getter
@Builder
@NoArgsConstructor
public class CustomerPurchaseStatisticDto {
    @JsonProperty("profile_id")
    private String customerId;
    @JsonProperty("order_count")
    private Integer orderCount;

    @JsonProperty("average_order_value")
    private Double averageOrderValue;

    @JsonProperty("total_order_value")
    private Double totalOrderValue;
}
