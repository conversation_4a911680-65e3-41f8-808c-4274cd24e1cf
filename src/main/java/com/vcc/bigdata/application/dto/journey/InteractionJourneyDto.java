package com.vcc.bigdata.application.dto.journey;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Builder
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InteractionJourneyDto {

    @JsonProperty("id")
    private String id;

    @JsonProperty("event_type")
    private String eventType;

    @JsonProperty("campaign_name")
    private String campaignName;

    @JsonProperty("from_email")
    private String fromEmail;

    @JsonProperty("to_email")
    private String toEmail;

    @JsonProperty("sender")
    private String sender;

    @JsonProperty("url")
    private String url;

    @JsonProperty("timestamp")
    private Instant timestamp;
}
