package com.vcc.bigdata.application.dto.log;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfileLogDto {

    @JsonProperty("id")
    private String id;

    @JsonProperty("profile_id")
    private String profileId;

    @JsonProperty("action_type")
    private String actionType;

    @JsonProperty("content")
    private String content;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("timestamp")
    private Instant timestamp;

    @JsonProperty("ip_address")
    private String ipAddress;

}
