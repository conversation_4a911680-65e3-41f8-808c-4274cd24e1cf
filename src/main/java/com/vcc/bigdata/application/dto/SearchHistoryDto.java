package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchHistoryDto {
    @JsonProperty("profile_id")
    private String profileId;

    @JsonProperty("keyword_freq")
    private List<KeywordFreq> keywordFreq;
}
