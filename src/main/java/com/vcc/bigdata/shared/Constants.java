package com.vcc.bigdata.shared;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;

public class Constants {
    // profile chart config
    public static final int CUSTOMER_CHART_SCORE = 1;
    public static final int CUSTOMER_CHART_PREDICTIVE_SCORE = 2;
    public static final int CUSTOMER_CHART_DEVICES = 3;
    public static final int CUSTOMER_CHART_SEARCH_HISTORY = 4;
    public static final int CUSTOMER_CHART_PURCHASES = 5;
    public static final int CUSTOMER_CHART_CHANNELS = 6;
    public static final int CUSTOMER_CHART_EMAIL_METRICS = 7;
    public static final int CUSTOMER_CHART_ACTIVITIES = 8;
    public static final int CUSTOMER_CHART_LOGS = 9;

    public static final List<Integer> CUSTOMER_LIST_CHART = List.of(CUSTOMER_CHART_SCORE,
            CUSTOMER_CHART_PREDICTIVE_SCORE,
            CUSTOMER_CHART_DEVICES,
            CUSTOMER_CHART_SEARCH_HISTORY,
            CUSTOMER_CHART_PURCHASES,
            CUSTOMER_CHART_CHANNELS,
            CUSTOMER_CHART_EMAIL_METRICS,
            CUSTOMER_CHART_ACTIVITIES,
            CUSTOMER_CHART_LOGS);

    public static final int GENDER_MALE=1;
    public static final int GENDER_FEMALE=2;
    public static final int GENDER_OTHER=3;

    public static final List<Integer> GENDER_LIST = List.of(GENDER_MALE,
            GENDER_FEMALE,
            GENDER_OTHER);
    public static final Integer BOOLEAN_FALSE = 0;
    public static final Integer BOOLEAN_TRUE = 1;
    public static final Integer AUDIENCE_TYPE_STATIC = 0;
    public static final Integer AUDIENCE_TYPE_DYNAMIC = 1;
    public static final String AND_EXPRESSION = "and";
    public static final String OR_EXPRESSION = "or";

    public static final String DATA_TYPE_DATETIME = "datetime";
    public static final String DATA_TYPE_DATE = "date";
    public static final String DATA_TYPE_TEXT = "text";
    public static final String DATA_TYPE_NUMBER = "number";
    public static final String DATA_TYPE_ARRAY = "array";
    public static final String DATA_TYPE_OBJECT = "object";
    public static final Integer MASKING_SYSTEM = -1;
    public static final Integer MASKING_FULL = 2;
    public static final Integer MASKING_PART = 1;

    public static final String DEMOGRAPHIC_FULLNAME = "fullname";
    public static final String DEMOGRAPHIC_PHONE = "phone";
    public static final String DEMOGRAPHIC_EMAIL = "email";
    public static final String DEMOGRAPHIC_GUID = "guid";
    public static final String DEMOGRAPHIC_FB_ID = "facebook";
    public static final String DEMOGRAPHIC_YOUTUBE_ID = "youtube";
    public static final String DEMOGRAPHIC_INSTAGRAM_ID = "instagram";
    public static final String DEMOGRAPHIC_TIKTOK_ID = "tiktok";
    public static final String DEMOGRAPHIC_ADDRESS = "address";
    public static final String DEMOGRAPHIC_LOCATION = "sidevn";
    public static final String DEMOGRAPHIC_CITY = "city";
    public static final String DEMOGRAPHIC_GENDER = "gender";
    public static final String DEMOGRAPHIC_BIRTHDAY = "birthday";
    public static final String DEMOGRAPHIC_EXTRA = "extra";

    public static ObjectMapper SERIALIZER = new ObjectMapper();

    static {
        SERIALIZER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        SERIALIZER.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
    }

}
