package com.vcc.bigdata.utility;

import com.google.common.hash.Hashing;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.zip.CRC32;

public class Hashings {
    private Hashings() {
        throw new IllegalStateException("Utility class");
    }

    public static Long CRC32(String input) {
        CRC32 crc32 = new CRC32();
        crc32.update(input.getBytes());
        return crc32.getValue();
    }

    public static String CRC32String(String input) {
        CRC32 crc32 = new CRC32();
        crc32.update(input.getBytes());
        return Long.toHexString(crc32.getValue());
    }

    public static String MD5(String input) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        md.update(input.getBytes());
        byte[] digest = md.digest();
        return DatatypeConverter.printHexBinary(digest).toLowerCase();
    }

    public static String sha1AsHex(String input) {
        return Hashing.sha1().hashString(input, Charset.forName("utf-8")).toString();
    }

    public static String hmacSha512(String input, String secretKey) {
        Mac sha512Hmac;
        String result = null;
        try {
            final byte[] byteKey = secretKey.getBytes(StandardCharsets.UTF_8);
            sha512Hmac = Mac.getInstance("HmacSHA512");
            SecretKeySpec keySpec = new SecretKeySpec(byteKey, "HmacSHA512");
            sha512Hmac.init(keySpec);
            byte[] macData = sha512Hmac.doFinal(input.getBytes(StandardCharsets.UTF_8));

            result = DatatypeConverter.printHexBinary(macData).toLowerCase();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }
}
