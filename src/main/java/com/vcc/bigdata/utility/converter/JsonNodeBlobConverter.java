package com.vcc.bigdata.utility.converter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.AttributeConverter;
import javax.sql.rowset.serial.SerialBlob;
import java.sql.Blob;


public class JsonNodeBlobConverter implements AttributeConverter<JsonNode, Blob> {
    private static final ObjectMapper om = new ObjectMapper();
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public Blob convertToDatabaseColumn(JsonNode attribute) {
        if (attribute == null) return null;
        try {
            byte[] value_in_bytes = attribute.toString().getBytes();
            return new SerialBlob(value_in_bytes);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public JsonNode convertToEntityAttribute(Blob dbData) {
        if (dbData == null) return null;
        JsonNode data = null;
        try {
            data = om.readTree(dbData.getBytes(1L, (int) dbData.length()));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return data;
    }
}
