package com.vcc.bigdata.utility;

import java.net.InetAddress;
import java.net.UnknownHostException;

public class Threads {
    public static void sleep(Long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (Exception ignored) {
        }
    }

    public static String getServerIP() {
        String ip = "127.0.0.1";
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            return inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        return ip;
    }

}
