package com.vcc.bigdata.utility;

import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.io.compress.Compression;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

public class HBaseUtils {
    private static final byte[] DEFAULT_DELIMITER = "|".getBytes();
    private static final byte DELIMITER_REPLICATOR = " ".getBytes()[0];
    private static final int DEFAULT_MAX_BUCKET = 1024;

    public static byte[] createCompositeKey(byte[]... keys) {
        return createCompositeKey(DEFAULT_DELIMITER[0], keys);
    }

    public static byte[] createCompositeKey(byte delimiter, byte[]... keys) {
        int length = (keys.length - 1);
        for (byte[] bytes : keys) {
            length += bytes.length;
            for (int i = 0; i < bytes.length; i++) {
                if (bytes[i] == DEFAULT_DELIMITER[0]) {
                    bytes[i] = DELIMITER_REPLICATOR;
                }
            }
        }

        ByteBuffer buffer = ByteBuffer.allocate(length);
        for (int i = 0; i < keys.length; i++) {
            if (i > 0) buffer.put(delimiter);
            buffer.put(keys[i]);
        }

        return buffer.array();
    }


    /**
     * Build composite key that salted with a bucket
     *
     * @param maxBucket max number of buckets
     * @param seed      use to generate bucket
     * @param keys      raw keys that will be used to generate bucket prefixed composite key
     * @return new key salted with bucket
     */
    public static byte[] buildCompositeKeyWithBucket(int maxBucket, String seed, byte[]... keys) {
        byte[] bucket = generateBucket(maxBucket, seed).getBytes();
        byte[] key = HBaseUtils.createCompositeKey(keys);

        ByteBuffer buff = ByteBuffer.allocate(bucket.length + key.length + 1);
        buff.put(bucket);
        buff.put(DEFAULT_DELIMITER[0]);
        buff.put(key);
        return buff.array();
    }

    /**
     * Build composite key that salted with default bucket
     * {@link HBaseUtils#DEFAULT_MAX_BUCKET}
     *
     * @param seed use to generate bucket
     * @param keys raw keys that will be used to generate bucket prefixed composite key
     * @return new key salted with bucket
     */
    public static byte[] buildCompositeKeyWithBucket(String seed, byte[]... keys) {
        return buildCompositeKeyWithBucket(DEFAULT_MAX_BUCKET, seed, keys);
    }

    public static String generateBucket(int maxBucket, String seed) {
        int bucket = Math.abs(HashingUtils.sha1DecodeAsHex(seed).hashCode() % maxBucket);
        return Strings.format("%03d", bucket);
    }

    public static void createTablesIfNotExists(List<byte[]> columnFamilies, Connection connection, TableName name) throws IOException {
        Admin admin = connection.getAdmin();
        if (admin.tableExists(name)) return;
        TableDescriptorBuilder tableDescriptorBuilder = TableDescriptorBuilder.newBuilder(name);
        List<ColumnFamilyDescriptor> familyDescriptors = new ArrayList<>();
        for (byte[] cf : columnFamilies) {
            ColumnFamilyDescriptorBuilder builder = ColumnFamilyDescriptorBuilder
                    .newBuilder(cf)
                    .setMaxVersions(3)
                    .setInMemory(true)
                    .setCompressionType(Compression.Algorithm.SNAPPY);
            familyDescriptors.add(builder.build());
        }
        tableDescriptorBuilder.setColumnFamilies(familyDescriptors);
        tableDescriptorBuilder.setCompactionEnabled(true);
        admin.createTable(tableDescriptorBuilder.build());
    }
}
