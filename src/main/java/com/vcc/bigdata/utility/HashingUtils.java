package com.vcc.bigdata.utility;

import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;

import java.nio.charset.StandardCharsets;

public class HashingUtils {
    public static String md5(String s) {
        return Hashing.sha1().hashString(s, Charsets.UTF_8).toString();
    }

    public static byte[] sha1(String input) {
        return Hashing.sha1().hashString(input, StandardCharsets.UTF_8).asBytes();
    }

    public static byte[] sha1(byte[] input) {
        return Hashing.sha1().hashBytes(input).asBytes();
    }

    public static String sha1AsHex(String input) {
        return Hashing.sha1().hashString(input, StandardCharsets.UTF_8).toString();
    }

    public static byte[] sha256(byte[] input) {
        return Hashing.sha256().hashBytes(input).asBytes();
    }


    public static String sha1AsHex(byte[] input) {
        return Hashing.sha1().hashBytes(input).toString();
    }

    public static String sha1AsBase64(String input, boolean urlSafe) {
        return Base64s.encodeAsString(sha1(input), urlSafe);
    }

    public static String sha256AsBase64(String input, boolean urlSafe) {
        return Base64s.encodeAsString(sha256(input.getBytes(StandardCharsets.UTF_8)), urlSafe);
    }

    public static String sha256AsBase64(String input) {
        return Base64s.encodeAsString(sha256(input.getBytes(StandardCharsets.UTF_8)), false);
    }

    public static String sha1AsBase64(byte[] input, boolean urlSafe) {
        return Base64s.encodeAsString(sha1(input), urlSafe);
    }

    public static String sha1DecodeAsHex(String input) {
        return Hashing.sha1().hashString(input, StandardCharsets.UTF_8).toString();
    }
}
