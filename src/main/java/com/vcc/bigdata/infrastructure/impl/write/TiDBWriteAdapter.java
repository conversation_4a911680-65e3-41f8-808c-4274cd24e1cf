package com.vcc.bigdata.infrastructure.impl.write;

import com.vcc.bigdata.infrastructure.impl.TiDBAdapter;
import lombok.extern.slf4j.Slf4j;

import java.sql.Statement;
import java.util.List;

@Slf4j
public class TiDBWriteAdapter extends TiDBAdapter {

    public TiDBWriteAdapter(String url, String username, String password) {
        super(url, username, password);
    }

    public boolean insertIgnore(String table, List<String> schema, List<String> values) {
        StringBuilder queryBuilder = new StringBuilder("INSERT IGNORE INTO ");
        queryBuilder.append(table);
        queryBuilder.append("(");
        queryBuilder.append(String.join(",", schema));
        queryBuilder.append(") VALUES");
        queryBuilder.append(String.join(",", values));
        try (Statement statement = getConnection().createStatement()) {
            statement.execute(queryBuilder.toString());
        } catch (Exception e) {
            log.error("Error insert tidb: {}", e.getMessage());
            return false;
        }
        return true;
    }

    public boolean deletedSegmentProfile(String table, long segmentId) {
        String query = String.format("UPDATE %s SET is_deleted=1 WHERE seg_id = %s", table, segmentId);
        try (Statement statement = getConnection().createStatement()) {
            statement.execute(query);
        } catch (Exception e) {
            log.error("Error deleted profile by segment id: {}", e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * Delete multiple profile by segmentation id from table
     * DELETE FROM {TABLE} WHERE seg_id = {segment_id} AND profile_id IN ('profile_id','profile_id',...)
     * @param table
     * @param segmentId
     * @param values list profile id
     * @return
     */
    public boolean deleteSegmentProfiles(String table, String segmentId, List<String> values) {
        StringBuilder queryBuilder = new StringBuilder("DELETE FROM ");
        queryBuilder.append(table);
        queryBuilder.append(" WHERE seg_id = ").append(segmentId);
        queryBuilder.append(" AND profile_id IN ('");
        queryBuilder.append(String.join("','", values));
        queryBuilder.append("')");
        try (Statement statement = getConnection().createStatement()) {
            statement.execute(queryBuilder.toString());
        } catch (Exception e) {
            log.error("Error insert tidb: {}", e.getMessage());
            return false;
        }
        return true;
    }

}
