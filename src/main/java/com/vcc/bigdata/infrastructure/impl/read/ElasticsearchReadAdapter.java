package com.vcc.bigdata.infrastructure.impl.read;

import com.vcc.bigdata.domain.model.Pair;
import com.vcc.bigdata.infrastructure.config.ElasticConfig;
import com.vcc.bigdata.infrastructure.impl.ElasticsearchAdapter;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Component
public class ElasticsearchReadAdapter extends ElasticsearchAdapter {

    public ElasticsearchReadAdapter(ElasticConfig config) {
        super(config);
    }

    public SearchResponse search(String index, BoolQueryBuilder bqb, List<Pair<String, SortOrder>> sorts, int limit, int page) throws IOException {
        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (bqb != null) searchSourceBuilder.query(bqb);
        searchSourceBuilder.size(limit);
        searchSourceBuilder.from((page) * limit);
        if (sorts != null) {
            for (Pair<String, SortOrder> sort : sorts) {
                searchSourceBuilder.sort(sort.getObject1(), sort.getObject2());
            }
        }
//        searchSourceBuilder.minScore(0.00001F);
        searchRequest.source(searchSourceBuilder);

        return getClient().search(searchRequest, RequestOptions.DEFAULT);
    }

    public SearchResponse searchAcrossMultipleIndices(String[] indices, BoolQueryBuilder bqb, List<Pair<String, SortOrder>> sorts, int limit, int page) throws IOException {
        SearchRequest searchRequest = new SearchRequest(indices);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (bqb != null) searchSourceBuilder.query(bqb);
        searchSourceBuilder.size(limit);
        searchSourceBuilder.from((page) * limit);
        if (sorts != null) {
            for (Pair<String, SortOrder> sort : sorts) {
                searchSourceBuilder.sort(sort.getObject1(), sort.getObject2());
            }
        }
//        searchSourceBuilder.minScore(0.00001F);
        searchRequest.source(searchSourceBuilder);

        return getClient().search(searchRequest, RequestOptions.DEFAULT);
    }

    public Map<String, Object> findById(String index, String id) throws IOException {
        GetRequest getRequest = new GetRequest(index, id);

        GetResponse getResponse = getClient().get(getRequest, RequestOptions.DEFAULT);

        return getResponse.getSource();
    }

    public GetResponse getById(String index, String id) throws IOException {
        GetRequest getRequest = new GetRequest(index, id);
        return getClient().get(getRequest, RequestOptions.DEFAULT);
    }

    public CountResponse countingDocument(String index, BoolQueryBuilder bqb) throws IOException {
        CountRequest countRequest = new CountRequest(index);
        if (bqb != null) countRequest.query(bqb);
        return getClient().count(countRequest, RequestOptions.DEFAULT);
    }

    public SearchResponse search(String index, SearchSourceBuilder searchSourceBuilder) throws IOException {
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(searchSourceBuilder);
        return getClient().search(searchRequest, RequestOptions.DEFAULT);
    }
}

