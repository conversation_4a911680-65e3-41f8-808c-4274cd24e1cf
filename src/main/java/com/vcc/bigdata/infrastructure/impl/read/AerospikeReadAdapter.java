package com.vcc.bigdata.infrastructure.impl.read;

import com.aerospike.client.AerospikeClient;
import com.aerospike.client.Host;
import com.aerospike.client.policy.ClientPolicy;
import com.vcc.bigdata.infrastructure.impl.IAerospikeAdapter;

public class AerospikeReadAdapter implements IAerospikeAdapter {

    private AerospikeClient client;

    public AerospikeReadAdapter(String clusters) {
        String[] nodes = clusters.split(",");
        Host[] hosts = new Host[nodes.length];
        for (int i = 0; i < hosts.length; i++) {
            String ip = nodes[i].split(":")[0];
            String port = nodes[i].split(":")[1];
            hosts[i] = new Host(ip, Integer.parseInt(port));
        }

        ClientPolicy clientPolicy = new ClientPolicy();
        clientPolicy.connPoolsPerNode = Runtime.getRuntime().availableProcessors() > 8 ? 2 : 1;
        clientPolicy.maxConnsPerNode = 1000;
        clientPolicy.timeout = 1000;
        clientPolicy.readPolicyDefault.maxRetries = 3;
        clientPolicy.writePolicyDefault.maxRetries = 3;

        this.client = new AerospikeClient(clientPolicy, hosts);
        Runtime.getRuntime().addShutdownHook(
                new Thread(() -> {
                    client.close();
                })
        );
    }

    @Override
    public AerospikeClient getAerospikeClient() {
        return client;
    }
}
