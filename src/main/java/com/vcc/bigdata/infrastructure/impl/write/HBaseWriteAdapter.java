package com.vcc.bigdata.infrastructure.impl.write;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Strings;
import com.vcc.bigdata.shared.Constants;
import com.vcc.bigdata.infrastructure.impl.HBaseAdapter;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Delete;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.util.Bytes;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public abstract class HBaseWriteAdapter<T> extends HBaseAdapter {
    protected static final byte[] CF = "cf".getBytes();
    private String namespace;

    public HBaseWriteAdapter(String zkHosts, int zkPort, String zkBasePath, String namespace) throws IOException {
        super(zkHosts, zkPort, zkBasePath, namespace);
        this.namespace = namespace;
    }

    public boolean add(String table, T object, Map<String, List<Object>> dataColumn) {
        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {
            tb.put(createPut(object, dataColumn));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return false;
        }

        return true;
    }

    public void addList(String table, List<T> objects, Map<T, JSONObject> dataColumns) {
        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {
            List<Put> puts = new ArrayList<>();
            objects.forEach(object -> {
                Put put = new Put(buildRowKey(object));
                JSONObject dataColumn = dataColumns.get(object);
                Set<String> keys = dataColumn.keySet();
                keys.forEach(key -> put.addColumn(CF, Bytes.toBytes(key), Bytes.toBytes(dataColumn.get(key).toString())));
                puts.add(put);
            });
            tb.put(puts);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public void addProfile(String table, T object, Map<String, String> data) {
        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {
            byte[] rowKey = buildRowKey(object);
            Put put = new Put(rowKey);
            data.forEach((k, v) -> put.addColumn(CF, Bytes.toBytes(k), Bytes.toBytes(v)));
            tb.put(put);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * batch put profile HBase
     * @param table
     * @param objects
     * @param dataList
     */
    public void updateListProfile(String table, List<T> objects, List<Map<String, String>> dataList) {
        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        List<Put> puts = new ArrayList<>();
        try (Table tb = getConnection().getTable(tableName)) {
            for (int i = 0; i < dataList.size(); i++) {
                byte[] rowKey = buildRowKey(objects.get(i));
                Put put = new Put(rowKey);
                dataList.get(i).forEach((k, v) -> put.addColumn(CF, Bytes.toBytes(k), Bytes.toBytes(v)));
                puts.add(put);
            }
            tb.put(puts);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public Put createPut(T object, Map<String, List<Object>> dataColumn) {
        byte[] rowKey = buildRowKey(object);
        Put put = new Put(rowKey);
        dataColumn.forEach((key, value) -> {
            try {
                put.addColumn(CF, Bytes.toBytes(key), Bytes.toBytes(Constants.SERIALIZER.writeValueAsString(value)));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });
        return put;
    }

    public boolean delete(String table, T object) {
        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);

        try (Table tb = getConnection().getTable(tableName)) {
            byte[] rowKey = buildRowKey(object);
            tb.delete(new Delete(rowKey));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return false;
        }
        return true;
    }

    // for profile history
    public boolean add(String table, String qualifier, T object) {
        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {
            tb.put(createPut(qualifier, object));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return false;
        }

        return true;
    }

    public boolean deleteColumn(String table, T object, String qualifier) {
        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {
            byte[] rowKey = buildRowKey(object);
            Delete delete = new Delete(rowKey);
            delete.addColumn(CF, Bytes.toBytes(qualifier));
            tb.delete(delete);
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            return false;
        }
        return true;
    }

    private Put createPut(String qualifier, T object) throws Exception {
        byte[] rowKey = buildRowKey(object);
        Put put = new Put(rowKey);

        if (object != null) {
            put.addColumn(CF, Bytes.toBytes(qualifier), Bytes.toBytes(Constants.SERIALIZER.writeValueAsString(object)));
        }
        return put;
    }

    // end of it
    protected abstract byte[] buildRowKey(T object);
}
