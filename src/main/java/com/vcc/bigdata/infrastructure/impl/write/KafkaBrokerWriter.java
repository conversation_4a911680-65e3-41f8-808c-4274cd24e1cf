package com.vcc.bigdata.infrastructure.impl.write;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.Future;

public class KafkaBrokerWriter {

    private final Producer<String, byte[]> producer;
    private final String topic;

    public KafkaBrokerWriter(Properties producerProperties, String topicName) {
        this.producer = new KafkaProducer<>(producerProperties);
        this.topic = topicName;
    }

    public Future<RecordMetadata> write(String message) {
        return producer.send(new ProducerRecord<>(topic, message.getBytes()));
    }

    /**
     * send list string message
     * @param messages List {@link String} message
     */
    public List<Future<RecordMetadata>> write(List<String> messages) {
        List<Future<RecordMetadata>> futures = new ArrayList<>(messages.size());
        for (String message : messages) {
            futures.add(producer.send(new ProducerRecord<>(topic, message.getBytes())));
        }
        return futures;
    }

    public Future<RecordMetadata> write(String message, Integer partition) {
        return producer.send(new ProducerRecord<>(topic, message.getBytes()));
    }
}
