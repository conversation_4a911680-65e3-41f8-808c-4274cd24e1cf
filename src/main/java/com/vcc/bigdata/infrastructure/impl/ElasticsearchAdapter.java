package com.vcc.bigdata.infrastructure.impl;

import com.vcc.bigdata.infrastructure.IElasticsearchAdapter;
import com.vcc.bigdata.infrastructure.config.ElasticConfig;
import com.vcc.bigdata.utility.http.HostAndPort;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ElasticsearchAdapter implements IElasticsearchAdapter {
    private final RestHighLevelClient client;
    protected Logger logger = LoggerFactory.getLogger(getClass());

    public ElasticsearchAdapter(ElasticConfig config) {
        HttpHost[] httpHosts = new HttpHost[config.getHosts().size()];

        int i = 0;
        for (HostAndPort hp : config.getHosts()) {
            httpHosts[i++] = new HttpHost(hp.getHost(), hp.getPort(), "http");
        }

//        RestClientBuilder clientBuilder = RestClient.builder(httpHosts);

        RestClientBuilder clientBuilder = RestClient.builder(httpHosts)
                .setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                        .setConnectTimeout(5000)  // Timeout khi kết nối (5 giây)
                        .setSocketTimeout(120000) // Timeout khi chờ phản hồi (120 giây = 2 phút)
                );
        if (config.isSecurity()) {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY, config.getAuthentication());
            clientBuilder.setHttpClientConfigCallback(httpAsyncClientBuilder ->
                    httpAsyncClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
        }
        this.client = new RestHighLevelClient(clientBuilder);
    }

    @Override
    public RestHighLevelClient getClient() {
        return client;
    }
}
