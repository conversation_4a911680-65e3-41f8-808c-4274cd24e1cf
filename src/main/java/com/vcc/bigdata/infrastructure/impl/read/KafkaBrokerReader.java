package com.vcc.bigdata.infrastructure.impl.read;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.vcc.bigdata.utility.Threads;
import com.vcc.bigdata.utility.mb.Message;
import com.vcc.bigdata.utility.mb.Messages;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Collection;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Pattern;

public abstract class KafkaBrokerReader {
    protected final Logger log = LoggerFactory.getLogger(getClass());
    private Collection<String> topics;
    private int numConsumers;
    private int minRecords;
    private int maxWaitSeconds;
    private Long previousTime;
    private ExecutorService executor;
    private Properties consumerProperties;
    private AtomicBoolean running = new AtomicBoolean(false);

    protected KafkaBrokerReader() {
    }

    protected KafkaBrokerReader(Properties consumerProperties, Collection<String> topics) {
        this(consumerProperties, topics, null, null, null);
    }
    protected KafkaBrokerReader(Properties consumerProperties, Collection<String> topics, int numConsumers) {
        this(consumerProperties, topics, numConsumers, null, null);
    }

    protected KafkaBrokerReader(Properties consumerProperties, Collection<String> topics, int numConsumers, int minRecords) {
        this(consumerProperties, topics, numConsumers, minRecords, null);
    }

    protected KafkaBrokerReader(Properties consumerProperties, Collection<String> topics, Integer numConsumers, Integer minRecords, Integer maxWaitSeconds) {
        this.consumerProperties = consumerProperties;
        this.minRecords = minRecords != null && minRecords > 0 ? minRecords : 1;
        this.numConsumers = numConsumers != null && numConsumers > 0 ? numConsumers : getDefaultNumConsumer();
        this.maxWaitSeconds = maxWaitSeconds != null && maxWaitSeconds > 0 ? maxWaitSeconds : 1;
        this.executor = Executors.newFixedThreadPool(this.numConsumers, new ThreadFactoryBuilder().setNameFormat(getClass().getSimpleName() + "-consumer-%d").build());
        setTopics(topics);
    }

    private int getDefaultNumConsumer() {
        return Math.max(Runtime.getRuntime().availableProcessors() - 1, 0);
    }

    public void start() {
        running = new AtomicBoolean(true);
        log.info("Starting {} consumer(s)", numConsumers);
        for (int i = 0; i < numConsumers; i++) {
            executor.submit(() -> startKafkaConsumer(topics));
        }
        previousTime = System.currentTimeMillis();
    }

    public void stop() {
        running.set(false);
        log.info("Stop Kafka queue reader");
        executor.shutdown();
    }

    private void startKafkaConsumer(Collection<String> topics) {
        if (topics.isEmpty()) {
            log.warn("Topic set is empty");
            stop();
        }
        while (running.get()) {
            try (KafkaConsumer<String, byte[]> consumer = new KafkaConsumer<>(consumerProperties)) {
                String patternTopic = "^.*(" + String.join("|", topics) + ")$";
                consumer.subscribe(Pattern.compile(patternTopic));

                Messages messageQueue = new Messages();
                while (running.get()) {
                    if (!allowPollMessage()) {
                        log.info("Wait for server available resource to poll message");
                        Threads.sleep(1000L);
                        continue;
                    }
                    ConsumerRecords<String, byte[]> records = consumer.poll(Duration.ofMillis(100));
                    for (ConsumerRecord<String, byte[]> message : records) {
                        messageQueue.add(new Message(message.value(), message.timestamp(), message.topic()));
                    }

                    if (messageQueue.size() >= minRecords || validateByWait(messageQueue)) {
//                        log.info("Receive " + messageQueue.size() + " message");
                        invokeHandlers(messageQueue);
                        consumer.commitSync();
                        messageQueue.clear();
                        previousTime = System.currentTimeMillis();
                    }
                }
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException("Un-known exception", e);
            }
        }
        log.info("Consumer stopped at thread: {}", Thread.currentThread().getName());
    }

    private boolean validateByWait(Messages queueRecords) {
        synchronized (this.getClass().getSimpleName()) {
            long waitTime = System.currentTimeMillis() - previousTime;
            boolean isOkQueue = queueRecords.size() > 0;
            boolean isOkTime = waitTime / 1000 >= maxWaitSeconds;

            return isOkQueue && isOkTime;
        }
    }

    public Collection<String> getTopics() {
        return topics;
    }

    public void setTopics(Collection<String> topics) {
        this.topics = topics;
    }

    protected abstract void invokeHandlers(Messages messages) throws Exception;

    protected abstract boolean allowPollMessage();

    public abstract boolean gracefulStop();
}
