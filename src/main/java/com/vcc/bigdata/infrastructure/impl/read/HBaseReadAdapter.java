package com.vcc.bigdata.infrastructure.impl.read;


import com.google.common.base.Strings;
import com.vcc.bigdata.infrastructure.impl.HBaseAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Table;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
public abstract class HBaseReadAdapter<T> extends HBaseAdapter {
    public static final byte[] CF = "cf".getBytes();
    private String namespace;

    public HBaseReadAdapter(String zkHosts, int zkPort, String zkBasePath, String namespace) throws IOException {
        super(zkHosts, zkPort, zkBasePath, namespace);
        this.namespace = namespace;
    }

    public List<JSONObject> getAllBase(String table, List<String> rowKeys) {
        List<JSONObject> data = new ArrayList<>();

        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {
            List<Get> gets = new ArrayList<>();
            for (String key : rowKeys) {
                gets.add(new Get(buildCommonRowKey(key, key)));
            }

            Result[] results = tb.get(gets);
            if (results == null || results.length == 0) return data;

            for (int i = 0; i < results.length; i++) {
                JSONObject object = new JSONObject();
                NavigableMap<byte[], byte[]> rsMap = results[i].getFamilyMap(CF);
                if (rsMap == null || rsMap.isEmpty()) continue;
                rsMap.forEach((kByte, vByte) -> {
                    String key = new String(kByte, StandardCharsets.UTF_8);
                    String value = new String(vByte, StandardCharsets.UTF_8);
                    object.put(key, value);
                });
                data.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public List<JSONObject> getAll(String table, List<String> ids, String type) {
        List<JSONObject> data = new ArrayList<>();

        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {
            List<Get> gets = new ArrayList<>();
            for (String id : ids) {
                if (type.equals("activity")) {
                    String[] idSplit = id.split("_");
                    gets.add(new Get(buildCommonRowKey(idSplit[0] + idSplit[1], idSplit[0], idSplit[1])));
                } else gets.add(new Get(buildCommonRowKey(id, id)));
            }

            Result[] results = tb.get(gets);
            if (results == null || results.length == 0) return data;

            for (int i = 0; i < results.length; i++) {
                JSONObject object = new JSONObject();
                object.put("data", new JSONObject());

                NavigableMap<byte[], byte[]> rsMap = results[i].getFamilyMap(CF);
                if (rsMap == null || rsMap.isEmpty()) continue;
                rsMap.forEach((kByte, vByte) -> {
                    String key = new String(kByte, StandardCharsets.UTF_8);
                    String value = new String(vByte, StandardCharsets.UTF_8);
                    if (type.equals("person")) {
                        if (key.startsWith("_"))
                            object.getJSONObject("data").put(key.substring(1), new JSONArray(value));
                        else if (key.equals("profile_id")) object.put(key, value);
                        else if (key.equals("last_update")) object.put(key, Long.parseLong(value));
                        else if (key.equals("sources")) object.put(key, new JSONArray(value));
                        else if (key.equals("is_delete") && !value.equalsIgnoreCase("null")) {
                            object.put(key, Integer.parseInt(value));
                        }
                    } else if (type.equals("activity")) {
                        if (key.startsWith("_"))
                            object.getJSONObject("data").put(key.substring(1), new JSONArray(value));
                        else if (key.equals("profile_id")) object.put(key, value);
                        else if (key.equals("time_checkpoint")) object.put(key, Long.parseLong(value));
                        else if (key.equals("activation_id")) object.put(key, value);
                        else if (key.equals("type")) object.put(key, value);
                    }
                });

                data.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return data;
    }

    public List<JSONObject> getAllV2(String table, List<String> ids, String type) {
        List<JSONObject> data = new ArrayList<>();

        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {
            List<Get> gets = new ArrayList<>();
            for (String id : ids) {
                if (type.equals("activity")) {
                    String[] idSplit = id.split("_");
                    gets.add(new Get(buildCommonRowKey(idSplit[0] + idSplit[1], idSplit[0], idSplit[1])));
                } else gets.add(new Get(buildCommonRowKey(id, id)));
            }

            Result[] results = tb.get(gets);
            if (results == null || results.length == 0) return data;

            for (int i = 0; i < results.length; i++) {
                JSONObject object = new JSONObject();
                object.put("data", new JSONObject());

                NavigableMap<byte[], byte[]> rsMap = results[i].getFamilyMap(CF);
                rsMap.forEach((kByte, vByte) -> {
                    String key = new String(kByte, StandardCharsets.UTF_8);
                    String value = new String(vByte, StandardCharsets.UTF_8);
                    if (type.equals("person")) {
                        if (key.startsWith("_"))
                            object.getJSONObject("data").put(key.substring(1), new JSONArray(value));
                        else if (key.equals("profile_id")) object.put(key, value);
                        else if (key.equals("last_update")) object.put(key, Long.parseLong(value));
                        else if (key.equals("sources")) object.put(key, new JSONArray(value));
                    } else if (type.equals("activity")) {
                        if (key.startsWith("_"))
                            object.getJSONObject("data").put(key.substring(1), new JSONArray(value));
                        else if (key.equals("profile_id")) object.put(key, value);
                        else if (key.equals("time_checkpoint")) object.put(key, Long.parseLong(value));
                        else if (key.equals("activation_id")) object.put(key, value);
                        else if (key.equals("type")) object.put(key, value);
                    }
                });

                data.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return data;
    }

    public JSONObject getById(String table, String id, String type) {
        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {
            Get get = new Get(buildCommonRowKey(id, id));
//            get.readAllVersions();
            Result result = tb.get(get);

            if (result == null) return null;

            JSONObject object = new JSONObject();
            object.put("data", new JSONObject());

            NavigableMap<byte[], byte[]> rsMap = result.getFamilyMap(CF);
            if (rsMap == null) {
                return null;
            }
            rsMap.forEach((kByte, vByte) -> {
                String key = new String(kByte, StandardCharsets.UTF_8);
                String value = new String(vByte, StandardCharsets.UTF_8);
                if (type.equals("person")) {
                    if (key.startsWith("_")) object.getJSONObject("data").put(key.substring(1), new JSONArray(value));
                    else if (key.equals("profile_id")) object.put(key, value);
                    else if (key.equals("last_update")) object.put(key, Long.parseLong(value));
                    else if (key.equals("sources")) object.put(key, new JSONArray(value));
                } else if (type.equals("insight")) {
                    if (key.equals("new_tor_info")) {
                        object.put(key, value);
                    } else if (key.equals("item_ids")) {
                        object.put(key, new JSONArray(value));
                    } else if (key.equals("timestamp")) {
                        object.put(key, Long.parseLong(value));
                    } else if (key.equals("ip")) {
                        object.put(key, value);
                    } else if (key.equals("cov")) {
                        object.put(key, Integer.parseInt(value));
                    } else if (key.equals("referrer")) {
                        object.put(key, value);
                    } else if (key.equals("behaviours")) {
                        object.put(key, value);
                    } else if (key.equals("frequency")) {
                        object.put(key, value);
                    }
                }
            });
            return object;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Lấy activation từ Hbase
     *
     * @param table
     * @param id
     * @return Map<String, List < String>>: key = page, value = list email, mỗi page lưu max 1000 emails
     */
    public Map<String, List<String>> getActivationData(String table, String id) {
        TableName tableName = Strings.isNullOrEmpty(namespace)
                ? TableName.valueOf(table)
                : TableName.valueOf(namespace + ":" + table);

        try (Table tb = getConnection().getTable(tableName)) {
            Get get = new Get(buildCommonRowKey(id, id));
            Result result = tb.get(get);

            if (result == null) return null;

            Map<String, List<String>> dataMap = new HashMap<>();

            NavigableMap<byte[], byte[]> rsMap = result.getFamilyMap(CF);
            if (rsMap == null) {
                return null;
            }

            rsMap.forEach((kByte, vByte) -> {
                String key = new String(kByte, StandardCharsets.UTF_8);
                String value = new String(vByte, StandardCharsets.UTF_8);

                try {
                    JSONArray jsonArray = new JSONArray(value); // Chuyển dữ liệu về JSONArray
                    List<String> valueList = new ArrayList<>();
                    for (int i = 0; i < jsonArray.length(); i++) {
                        valueList.add(jsonArray.getString(i));
                    }
                    dataMap.put(key, valueList);
                } catch (Exception e) {
                    // Nếu không phải JSON Array, lưu giá trị đơn dưới dạng danh sách 1 phần tử
                    dataMap.put(key, Collections.singletonList(value));
                }
            });

            return dataMap;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    public String findByColumn(String table, T object, String column) {
        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {

            Get get = new Get(buildRowKey(object));
            Result result = tb.get(get);

            if (result.isEmpty()) {
                return null;
            }

            NavigableMap<byte[], byte[]> rsMap = result.getFamilyMap(CF);
            return new String(rsMap.get(column.getBytes()));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<String> getListColumn(String table, T object) {
        List<String> columns = new ArrayList<>();

        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {

            Get get = new Get(buildRowKey(object));
            Result result = tb.get(get);

            if (result.isEmpty()) {
                return null;
            }

            NavigableMap<byte[], byte[]> rsMap = result.getFamilyMap(CF);
            rsMap.forEach((k, v) -> columns.add(new String(k)));

            return columns;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public TreeMap<String, String> findById(String table, T object) {
        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {
            Get get = new Get(buildRowKey(object));
            Result result = tb.get(get);

            if (result.isEmpty()) {
                return null;
            }
            NavigableMap<byte[], byte[]> rsMap = result.getFamilyMap(CF);
            TreeMap<String, String> profileHistoryMap = new TreeMap<>((k1, k2) -> {
                Long key1 = Long.parseLong(k1);
                Long key2 = Long.parseLong(k2);
                return key2.compareTo(key1);
            });

            rsMap.forEach((qualifier, value) -> profileHistoryMap.put(new String(qualifier), new String(value)));
            return profileHistoryMap;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<JSONObject> getList(String table, List<T> objects) {
        List<JSONObject> jsonObjects = new ArrayList<>();
        TableName tableName = Strings.isNullOrEmpty(namespace) ? TableName.valueOf(table) : TableName.valueOf(namespace + ":" + table);
        try (Table tb = getConnection().getTable(tableName)) {
            List<Get> gets = new ArrayList<>();
            for (T object : objects) {
                Get get = new Get(buildRowKey(object));
                gets.add(get);
            }
            Result[] results = tb.get(gets);
            for (Result result : results) {
                if (result.isEmpty()) {
                    continue;
                }
                NavigableMap<byte[], byte[]> rsMap = result.getFamilyMap(CF);
                JSONObject jsonObject = new JSONObject();
                for (Map.Entry<byte[], byte[]> entry : rsMap.entrySet()) {
                    String key = new String(entry.getKey(), StandardCharsets.UTF_8);
                    String value = new String(entry.getValue(), StandardCharsets.UTF_8);
                    jsonObject.put(key, value);
                }
                jsonObjects.add(jsonObject);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonObjects;
    }

    protected abstract byte[] buildRowKey(T object);
}
