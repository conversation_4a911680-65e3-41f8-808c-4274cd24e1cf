package com.vcc.bigdata.infrastructure.impl;

import com.vcc.bigdata.infrastructure.ITiDBAdapter;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class TiDBAdapter implements ITiDBAdapter {

    private Connection connection;

    public TiDBAdapter(String url, String username, String password) {
        try {
            this.connection = DriverManager.getConnection(url, username, password);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Connection getConnection() {
        return this.connection;
    }

    @Override
    public void close() {
        try {
            this.connection.close();
        } catch (SQLException e) {
            throw new RuntimeException();
        }
    }
}
