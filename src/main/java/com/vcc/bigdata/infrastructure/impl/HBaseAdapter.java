package com.vcc.bigdata.infrastructure.impl;

import com.vcc.bigdata.infrastructure.IHBaseAdapter;
import com.vcc.bigdata.utility.HBaseUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Arrays;

public class HBaseAdapter implements IHBaseAdapter {
    protected static final byte[] CF = "cf".getBytes();
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    private final Connection connection;
    private final Configuration config;
    private final String NAMESPACE;

    public HBaseAdapter(String zkHosts, int zkPort, String zkBasePath, String namespace) throws IOException {

        this.config = HBaseConfiguration.create();
        this.config.set(HConstants.ZOOKEEPER_QUORUM, zkHosts);
        this.config.setInt(HConstants.ZOOKEEPER_CLIENT_PORT, zkPort);
        this.config.set(HConstants.ZOOKEEPER_ZNODE_PARENT, zkBasePath);
        this.config.setInt(HConstants.HBASE_RPC_TIMEOUT_KEY, 10000);
        this.NAMESPACE = namespace;
        this.connection = ConnectionFactory.createConnection(config);
    }

    private static byte[] buildRowKey(String seed, byte[]... components) {
        return HBaseUtils.buildCompositeKeyWithBucket(seed, components);
    }

    public static byte[] buildCommonRowKey(String seed, Object... components) {
        byte[][] keyComponents = Arrays.stream(components).map(m -> m.toString().getBytes()).toArray(byte[][]::new);
        return buildRowKey(seed, keyComponents);
    }

    @Override
    public Connection getConnection() {
        return this.connection;
    }

    public Configuration getConfiguration() {
        return this.config;
    }
}