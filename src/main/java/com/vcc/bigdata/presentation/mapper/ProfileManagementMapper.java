package com.vcc.bigdata.presentation.mapper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.vcc.bigdata.shared.Constants;
import com.vcc.bigdata.application.dto.ProfileManagementDto;
import com.vcc.bigdata.domain.entity.ProfileManagement;

import java.util.List;

public class ProfileManagementMapper {
    private ProfileManagementMapper() {
        throw new IllegalStateException("This is utility class");
    }

    public static ProfileManagement dto2Entity(ProfileManagementDto profileManagementDto) {
        ProfileManagement profileManagement = new ProfileManagement();
        profileManagement.setId(profileManagementDto.getId() == null ? null : Long.parseLong(profileManagementDto.getId()));
        profileManagement.setName(profileManagementDto.getName());
        profileManagement.setUserId(Long.parseLong(profileManagementDto.getUserId()));
        profileManagement.setStatus(profileManagementDto.getStatus());
        profileManagement.setCreateTime(profileManagementDto.getCreateTime());
        profileManagement.setUpdateTime(profileManagementDto.getUpdateTime());
        profileManagement.setKmsCustomerId(profileManagementDto.getKmsCustomerId());

        try {
            if (profileManagementDto.getMapping() != null) {
                JsonNode mapping = Constants.SERIALIZER.readTree(Constants.SERIALIZER.writeValueAsString(profileManagementDto.getMapping()));
                profileManagement.setMapping(mapping);
            }

            if (profileManagementDto.getSources() != null) {
                JsonNode sources = Constants.SERIALIZER.readTree(Constants.SERIALIZER.writeValueAsString(profileManagementDto.getSources()));
                profileManagement.setSources(sources);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return profileManagement;
    }

    public static ProfileManagementDto entity2Dto(ProfileManagement profileManagement) {
        ProfileManagementDto profileManagementDto = new ProfileManagementDto();
        profileManagementDto.setId(profileManagement.getId() == null ? null : String.valueOf(profileManagement.getId()));
        profileManagementDto.setName(profileManagement.getName());
        profileManagementDto.setUserId(String.valueOf(profileManagement.getUserId()));
        profileManagementDto.setStatus(profileManagement.getStatus());
        profileManagementDto.setCreateTime(profileManagement.getCreateTime());
        profileManagementDto.setUpdateTime(profileManagement.getUpdateTime());
        profileManagementDto.setKmsCustomerId(profileManagement.getKmsCustomerId());

        try {
            if (profileManagement.getMapping() != null) {
                List<ProfileManagementDto.Mapping> mapping = Constants.SERIALIZER.readValue(profileManagement.getMapping().toString(), new TypeReference<List<ProfileManagementDto.Mapping>>() {
                });
                profileManagementDto.setMapping(mapping);
            }

            if (profileManagement.getSources() != null) {
                List<ProfileManagementDto.SourceInfo> sources = Constants.SERIALIZER.readValue(profileManagement.getSources().toString(), new TypeReference<List<ProfileManagementDto.SourceInfo>>() {
                });
                profileManagementDto.setSources(sources);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return profileManagementDto;
    }
}
