package com.vcc.bigdata.presentation.mapper;

import com.fasterxml.jackson.databind.JsonNode;
import com.vcc.bigdata.application.dto.AudienceDto;
import com.vcc.bigdata.domain.entity.Audience;
import com.vcc.bigdata.shared.Constants;

public class AudienceMapper {

    private AudienceMapper() {
        throw new IllegalStateException("This is utility class");
    }

    public static Audience dto2Entity(AudienceDto audienceDto) {
        Audience audience = new Audience();
        audience.setId(audienceDto.getId() == null ? null : Long.parseLong(audienceDto.getId()));
        audience.setName(audienceDto.getName());
        audience.setDescription(audienceDto.getDescription());
        audience.setUserId(Long.valueOf(audienceDto.getUserId()));
        audience.setIsDeleted(audienceDto.getIsDeleted());
        audience.setCreatedAt(audienceDto.getCreatedAt());
        audience.setUpdatedAt(audienceDto.getUpdatedAt());
        audience.setType(audienceDto.getType());
        try {
            if (audienceDto.getSegmentRule() != null) {
                JsonNode segmentRule = Constants.SERIALIZER.readTree(Constants.SERIALIZER.writeValueAsString(audienceDto.getSegmentRule()));
                audience.setSegmentRule(segmentRule);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return audience;
    }

    public static AudienceDto entity2Dto(Audience audience) {
        AudienceDto audienceDto = new AudienceDto();
        audienceDto.setId(audience.getId() == null ? null : String.valueOf(audience.getId()));
        audienceDto.setName(audience.getName());
        audienceDto.setDescription(audience.getDescription());
        audienceDto.setUserId(String.valueOf(audience.getUserId()));
        audienceDto.setIsDeleted(audience.getIsDeleted());
        audienceDto.setCreatedAt(audience.getCreatedAt());
        audienceDto.setUpdatedAt(audience.getUpdatedAt());
        audienceDto.setType(audience.getType());
        try {
            if (audience.getSegmentRule() != null) {
                AudienceDto.SegmentRule segmentRule = Constants.SERIALIZER.readValue(audience.getSegmentRule().toString(), AudienceDto.SegmentRule.class);
                audienceDto.setSegmentRule(segmentRule);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return audienceDto;
    }
}
