package com.vcc.bigdata.presentation.response.journey;

import com.vcc.bigdata.application.dto.journey.EdgeDto;
import com.vcc.bigdata.application.dto.journey.JourneyStepDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JourneyMapResponse {
    private List<JourneyStepDto> steps;
    private List<EdgeDto> edges;
}
