package com.vcc.bigdata.presentation.response.audience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AudienceResponse<T> {
    private T data;
    private Meta meta;

    public AudienceResponse() {
        this.data = null;
        this.meta = new Meta();
    }

    public AudienceResponse(T data) {
        this.data = data;
        this.meta = new Meta();
    }

    public static <T> AudienceResponse<T> empty() {
        return new AudienceResponse<>(null);
    }

    @Data
    public static class Meta {
        private String timestamp;
        private int page;
        private int limit;
        private int total;

        public Meta(Integer page, Integer limit, int size) {
            this.timestamp = LocalDateTime.now().toString();
            this.page = page;
            this.limit = limit;
            this.total = size;
        }

        public Meta() {
            this.timestamp = LocalDateTime.now().toString();
        }
    }
}
