package com.vcc.bigdata.presentation.controller.journey;

import com.vcc.bigdata.domain.PagingPayload;
import com.vcc.bigdata.application.dto.journey.CampaignDto;
import com.vcc.bigdata.application.dto.journey.InteractionJourneyDto;
import com.vcc.bigdata.application.dto.journey.JourneySummaryDto;
import com.vcc.bigdata.domain.service.journey.CampaignService;
import com.vcc.bigdata.domain.service.journey.InteractionJourneyService;
import com.vcc.bigdata.domain.service.journey.JourneyService;
import com.vcc.bigdata.presentation.response.Response;
import com.vcc.bigdata.presentation.response.ResponseFactory;
import com.vcc.bigdata.presentation.response.journey.JourneyMapResponse;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller quản lý các API liên quan đến hành trình khách hàng (profile journey).
 */
@RestController
@AllArgsConstructor
public class ProfileJourneyController {

    private final JourneyService journeyService;
    private final InteractionJourneyService interactionService;
    private final CampaignService campaignService;

    /**
     * Lấy sơ đồ hành trình (journey map) của khách hàng.
     *
     * @param profileId  ID khách hàng
     * @param from        Ngày bắt đầu (yyyy-MM-dd)
     * @param to          Ngày kết thúc (yyyy-MM-dd)
     * @param channelType Kênh tương tác (email, sms, push, ...)
     * @param campaignId  ID chiến dịch (tùy chọn)
     * @return Sơ đồ hành trình của khách hàng
     */
    @GetMapping("profile/{id}/journey-map")
    public Response<?> getJourneyMap(
            @PathVariable("id") String profileId,
            @RequestParam(value = "from_date", required = false) String from,
            @RequestParam(value = "to_date", required = false) String to,
            @RequestParam(value = "channel_type", required = false) String channelType,
            @RequestParam(value = "campaign_id", required = false) String campaignId
    ) {
        try {
            JourneyMapResponse result = journeyService.getProfileJourney(
                    Long.parseLong(profileId), from, to, channelType, campaignId
            );
            return ResponseFactory.getSuccessResponse("success", result);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse("Lỗi: " + e.getMessage());
        }
    }

    /**
     * Lấy danh sách tương tác email trong hành trình của khách hàng.
     *
     * @param profileId  ID khách hàng
     * @param from        Ngày bắt đầu (yyyy-MM-dd)
     * @param to          Ngày kết thúc (yyyy-MM-dd)
     * @param query       Từ khóa tìm kiếm
     * @param campaignId  ID chiến dịch
     * @param eventType   Loại sự kiện (open, click, ...)
     * @param offset      Trang hiện tại
     * @param limit       Số lượng bản ghi mỗi trang
     * @return Danh sách tương tác email
     */
    @GetMapping("profile/{id}/interactions/email")
    public Response<?> getEmailInteractions(
            @PathVariable("id") String profileId,
            @RequestParam(value = "from_date", required = false) String from,
            @RequestParam(value = "to_date", required = false) String to,
            @RequestParam(value = "search_query", required = false) String query,
            @RequestParam(value = "campaign_id", required = false) String campaignId,
            @RequestParam(value = "event_type", required = false) String eventType,
            @RequestParam(value = "offset", defaultValue = "1") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit
    ) {
        try {
            PagingPayload<InteractionJourneyDto> result = interactionService.getInteractions("email", profileId, from, to, query, campaignId, eventType, offset, limit);
            return ResponseFactory.getSuccessResponse("success", result);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse("Lỗi: " + e.getMessage());
        }
    }

    /**
     * Lấy danh sách tương tác SMS trong hành trình của khách hàng.
     *
     * @param profileId  ID khách hàng
     * @param from        Ngày bắt đầu (yyyy-MM-dd)
     * @param to          Ngày kết thúc (yyyy-MM-dd)
     * @param query       Từ khóa tìm kiếm
     * @param campaignId  ID chiến dịch
     * @param eventType   Loại sự kiện
     * @param offset      Trang hiện tại
     * @param limit       Số lượng bản ghi mỗi trang
     * @return Danh sách tương tác SMS
     */
    @GetMapping("profile/{id}/interactions/sms")
    public Response<?> getSmsInteractions(
            @PathVariable("id") String profileId,
            @RequestParam(value = "from_date", required = false) String from,
            @RequestParam(value = "to_date", required = false) String to,
            @RequestParam(value = "search_query", required = false) String query,
            @RequestParam(value = "campaign_id", required = false) String campaignId,
            @RequestParam(value = "event_type", required = false) String eventType,
            @RequestParam(value = "offset", defaultValue = "1") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit
    ) {
        try {
            PagingPayload<InteractionJourneyDto> result = interactionService.getInteractions("sms", profileId, from, to, query, campaignId, eventType, offset, limit);
            return ResponseFactory.getSuccessResponse("success", result);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse("Lỗi: " + e.getMessage());
        }
    }

    /**
     * Lấy danh sách tương tác push notification trong hành trình của khách hàng.
     *
     * @param profileId  ID khách hàng
     * @param from        Ngày bắt đầu (yyyy-MM-dd)
     * @param to          Ngày kết thúc (yyyy-MM-dd)
     * @param query       Từ khóa tìm kiếm
     * @param campaignId  ID chiến dịch
     * @param eventType   Loại sự kiện
     * @param offset      Trang hiện tại
     * @param limit       Số lượng bản ghi mỗi trang
     * @return Danh sách tương tác push
     */
    @GetMapping("profile/{id}/interactions/push")
    public Response<?> getPushInteractions(
            @PathVariable("id") String profileId,
            @RequestParam(value = "from_date", required = false) String from,
            @RequestParam(value = "to_date", required = false) String to,
            @RequestParam(value = "search_query", required = false) String query,
            @RequestParam(value = "campaign_id", required = false) String campaignId,
            @RequestParam(value = "event_type", required = false) String eventType,
            @RequestParam(value = "offset", defaultValue = "1") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit
    ) {
        try {
            PagingPayload<InteractionJourneyDto> result = interactionService.getInteractions("push", profileId, from, to, query, campaignId, eventType, offset, limit);
            return ResponseFactory.getSuccessResponse("success", result);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse("Lỗi: " + e.getMessage());
        }
    }

    /**
     * Lấy danh sách các bước định nghĩa trong hành trình khách hàng.
     *
     * @return Danh sách các bước (step) trong hành trình
     */
    @GetMapping("/journey-steps-definition")
    public Response<?> getJourneyStepDefinitions() {
        try {
            List<String> steps = journeyService.getStepDefinitions();
            return ResponseFactory.getSuccessResponse("success", steps);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse("Lỗi: " + e.getMessage());
        }
    }

    /**
     * Lấy danh sách mock chiến dịch phục vụ cho journey filter.
     *
     * @return Danh sách chiến dịch
     */
    @GetMapping("/campaigns")
    public Response<?> getCampaigns() {
        try {
            List<CampaignDto> campaigns = campaignService.getMockCampaigns();
            return ResponseFactory.getSuccessResponse("success", campaigns);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse("Lỗi: " + e.getMessage());
        }
    }

    /**
     * Lấy thông tin tổng quan hành trình của khách hàng.
     *
     * @param profileId ID khách hàng
     * @return Tổng quan hành trình (số bước, kênh, tương tác...)
     */
    @GetMapping("/profile/{id}/journey-summary")
    public Response<?> getJourneySummary(@PathVariable("id") String profileId) {
        try {
            JourneySummaryDto summary = journeyService.getJourneySummary(Long.parseLong(profileId));
            return ResponseFactory.getSuccessResponse("success", summary);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse("Lỗi: " + e.getMessage());
        }
    }

}
