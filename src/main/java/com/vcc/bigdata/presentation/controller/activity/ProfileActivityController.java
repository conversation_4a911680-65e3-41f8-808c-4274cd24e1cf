package com.vcc.bigdata.presentation.controller.activity;

import com.vcc.bigdata.domain.PagingPayload;
import com.vcc.bigdata.application.dto.activity.CSKHActivityDto;
import com.vcc.bigdata.application.dto.activity.EventDto;
import com.vcc.bigdata.application.dto.activity.InteractionDto;
import com.vcc.bigdata.application.dto.activity.OrderDto;
import com.vcc.bigdata.domain.service.activity.*;
import com.vcc.bigdata.presentation.response.Response;
import com.vcc.bigdata.presentation.response.ResponseFactory;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * Controller xử lý các API liên quan đến lịch sử hoạt động của khách hàng.
 * Bao gồm: lịch sử truy cập, tương tác, đơn hàng, CSKH, tổng hợp hoạt động và loại hoạt động.
 */
@RestController
@AllArgsConstructor
public class ProfileActivityController {
    private final EventService eventService;
    private final InteractionService interactionService;
    private final OrderService orderService;
    private final CSKHActivityService cskhActivityService;
    private final ActivityService activityService;

    /**
     * API lấy lịch sử truy cập của khách hàng.
     *
     * @param profileId  ID của khách hàng
     * @param limit       Số lượng kết quả trên mỗi trang (mặc định: 10)
     * @param offset      Vị trí bắt đầu trang (mặc định: 1)
     * @param from        Ngày bắt đầu lọc (định dạng ISO, tùy chọn)
     * @param to          Ngày kết thúc lọc (định dạng ISO, tùy chọn)
     * @param query       Từ khóa tìm kiếm (tùy chọn)
     * @return Lịch sử truy cập dưới dạng phân trang
     */
    @GetMapping("profile/{id}/activity/access-history")
    public Response<?> getEvents(
            @PathVariable("id") String profileId,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit,
            @RequestParam(value = "offset", defaultValue = "1") Integer offset,
            @RequestParam(value = "from_date", required = false) String from,
            @RequestParam(value = "to_date", required = false) String to,
            @RequestParam(value = "search_query", required = false) String query

    ) {
        try {
            PagingPayload<EventDto> result = eventService.getEvents(Long.parseLong(profileId), limit, offset, from, to, query);

            return ResponseFactory.getSuccessResponse("success", result);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse(e.getMessage());
        }
    }

    /**
     * API lấy lịch sử tương tác của khách hàng (email, FB, tổng đài, v.v.).
     *
     * @param profileId  ID khách hàng
     * @param from        Ngày bắt đầu lọc (tùy chọn)
     * @param to          Ngày kết thúc lọc (tùy chọn)
     * @param type        Loại tương tác (tùy chọn)
     * @param query       Từ khóa tìm kiếm (tùy chọn)
     * @param offset      Trang bắt đầu (mặc định: 1)
     * @param limit       Số lượng kết quả mỗi trang (mặc định: 10)
     * @return Danh sách tương tác dưới dạng phân trang
     */
    @GetMapping("profile/{id}/activity/interactions")
    public Response<?> getInteractions(
            @PathVariable("id") String profileId,
            @RequestParam(value = "from_date", required = false) String from,
            @RequestParam(value = "to_date", required = false) String to,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "search_query", required = false) String query,
            @RequestParam(value = "offset", defaultValue = "1") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit
    ) {
        try {
            PagingPayload<InteractionDto> result = interactionService.getInteractions(Long.parseLong(profileId), from, to, type, query, offset, limit);

            return ResponseFactory.getSuccessResponse("success", result);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse(e.getMessage());
        }
    }

    /**
     * API lấy danh sách đơn hàng của khách hàng.
     *
     * @param profileId  ID khách hàng
     * @param status      Trạng thái đơn hàng (tùy chọn)
     * @param from        Ngày bắt đầu lọc (tùy chọn)
     * @param to          Ngày kết thúc lọc (tùy chọn)
     * @param query       Từ khóa tìm kiếm đơn hàng hoặc sản phẩm (tùy chọn)
     * @param offset      Trang bắt đầu (mặc định: 1)
     * @param limit       Số lượng mỗi trang (mặc định: 10)
     * @return Danh sách đơn hàng dưới dạng phân trang
     */
    @GetMapping("profile/{id}/orders")
    public Response<?> getProfileOrders(
            @PathVariable("id") String profileId,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "from_date", required = false) String from,
            @RequestParam(value = "to_date", required = false) String to,
            @RequestParam(value = "search_query", required = false) String query,
            @RequestParam(value = "offset", defaultValue = "1") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit
    ) {
        try {
            PagingPayload<OrderDto> result = orderService.getProfileOrders(Long.parseLong(profileId), status, from, to, query, offset, limit);

            return ResponseFactory.getSuccessResponse("success", result);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse(e.getMessage());
        }
    }

    /**
     * API lấy lịch sử CSKH (chăm sóc khách hàng) của khách hàng.
     *
     * @param profileId  ID khách hàng
     * @param from        Ngày bắt đầu lọc (tùy chọn)
     * @param to          Ngày kết thúc lọc (tùy chọn)
     * @param query       Từ khóa tìm kiếm (tùy chọn)
     * @param offset      Trang bắt đầu (mặc định: 1)
     * @param limit       Số lượng mỗi trang (mặc định: 10)
     * @return Danh sách hoạt động CSKH dưới dạng phân trang
     */
    @GetMapping("profile/{id}/cskh-history")
    public Response<?> getCSKHHistory(
            @PathVariable("id") String profileId,
            @RequestParam(value = "from_date", required = false) String from,
            @RequestParam(value = "to_date", required = false) String to,
            @RequestParam(value = "search_query", required = false) String query,
            @RequestParam(value = "offset", defaultValue = "1") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit
    ) {
        try {
            PagingPayload<CSKHActivityDto> result = cskhActivityService.getCSKHHistory(Long.valueOf(profileId), from, to, query, offset, limit);

            return ResponseFactory.getSuccessResponse("success", result);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse(e.getMessage());
        }
    }

    /**
     * API lấy thông tin tổng hợp hoạt động của khách hàng.
     *
     * @param profileId ID khách hàng
     * @return Tổng số lượng các loại hoạt động (truy cập, tương tác, đơn hàng, CSKH, v.v.)
     */
    @GetMapping("profile/{id}/activity/summary")
    public Response<?> getActivitySummary(
            @PathVariable("id") String profileId
    ) {
        try {
            Map<String, Integer> result = activityService.getSummary(Long.valueOf(profileId));
            return ResponseFactory.getSuccessResponse("success", result);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse(e.getMessage());
        }
    }

    /**
     * API trả về danh sách các loại hoạt động hỗ trợ trong hệ thống.
     *
     * @return Danh sách loại hoạt động dạng chuỗi
     */
    @GetMapping("/activity-types")
    public Response<?> getActivityTypes() {
        try {
            List<String> result = activityService.getActivityTypes();
            return ResponseFactory.getSuccessResponse("success", result);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse(e.getMessage());
        }
    }
}
