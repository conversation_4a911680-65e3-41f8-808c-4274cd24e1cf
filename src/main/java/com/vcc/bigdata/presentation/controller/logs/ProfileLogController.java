package com.vcc.bigdata.presentation.controller.logs;

import com.vcc.bigdata.domain.PagingPayload;
import com.vcc.bigdata.application.dto.log.ProfileLogDto;
import com.vcc.bigdata.domain.service.log.ProfileLogService;
import com.vcc.bigdata.presentation.response.Response;
import com.vcc.bigdata.presentation.response.ResponseFactory;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller quản lý các API liên quan đến log hoạt động của khách hàng (Profile Logs).
 */
@RestController
@AllArgsConstructor
public class ProfileLogController {

    private final ProfileLogService profileLogService;

    /**
     * <PERSON><PERSON><PERSON> danh sách các log hoạt động của khách hàng.
     *
     * @param profileId ID khách hàng
     * @param from       Ngày bắt đầu lọc log (định dạng yyyy-MM-dd), tùy chọn
     * @param to         Ngày kết thúc lọc log (định dạng yyyy-MM-dd), tùy chọn
     * @param query      Từ khóa tìm kiếm trong nội dung log (nếu có)
     * @param type       Loại log (ví dụ: "create", "update", "delete",...), tùy chọn
     * @param offset     Trang hiện tại (mặc định = 1)
     * @param limit      Số lượng log mỗi trang (mặc định = 10)
     * @return Danh sách log của khách hàng kèm thông tin phân trang
     */
    @GetMapping("profile/{id}/logs")
    public Response<?> getInteractions(
            @PathVariable("id") String profileId,
            @RequestParam(value = "from_date", required = false) String from,
            @RequestParam(value = "to_date", required = false) String to,
            @RequestParam(value = "search_query", required = false) String query,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "offset", defaultValue = "1") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit
    ) {
        try {
            PagingPayload<ProfileLogDto> result = profileLogService.getProfileLogs(Long.parseLong(profileId), from, to, query, type, offset, limit);

            return ResponseFactory.getSuccessResponse("success", result);
        } catch (Exception e) {
            return ResponseFactory.getClientErrorResponse(e.getMessage());
        }
    }

}
