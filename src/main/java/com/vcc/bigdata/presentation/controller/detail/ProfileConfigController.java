package com.vcc.bigdata.presentation.controller.detail;

import com.vcc.bigdata.application.dto.ProfileConfig;
import com.vcc.bigdata.presentation.response.Response;
import com.vcc.bigdata.presentation.response.ResponseFactory;
import com.vcc.bigdata.shared.Constants;
import org.springframework.web.bind.annotation.*;

import javax.ws.rs.HeaderParam;

@RestController
public class ProfileConfigController {
    @GetMapping("/profile/{id}/config-all")
    public Response<ProfileConfig> getProfileConfig(@PathVariable String id,
                                                    @HeaderParam("user-id") String userId) {
        return ResponseFactory.getSuccessResponse("Mock api", ProfileConfig.builder().profileId(id).listChart(Constants.CUSTOMER_LIST_CHART).build());
    }

    @PostMapping("profile/{id}/config")
    public Response<ProfileConfig> postProfileConfig(@PathVariable String id,
                                                     @HeaderParam("user-id") String userId,
                                                     @RequestBody ProfileConfig.ProfileConfigInfo info) {
        return ResponseFactory.getSuccessResponse("Mock api", null);
    }
    @DeleteMapping("profile/{id}/config")
    public Response<ProfileConfig> deleteProfileConfig(@PathVariable String id,
                                                     @HeaderParam("user-id") String userId,
                                                     @RequestBody ProfileConfig.ProfileConfigInfo info) {
        return ResponseFactory.getSuccessResponse("Mock api", null);
    }

}
