package com.vcc.bigdata.presentation.controller.detail;

import com.vcc.bigdata.application.dto.*;
import com.vcc.bigdata.presentation.response.Response;
import com.vcc.bigdata.presentation.response.ResponseFactory;
import com.vcc.bigdata.shared.Constants;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.*;

@RestController
public class ProfileInfoController {
    /**
     * Retrieves the profile information of a customer.
     * @param id the ID of the customer whose profile is to be retrieved
     * @return the profile information of the customer
     */
    @GetMapping("/profile/{id}")
    public Response<ProfileInfoDto> getProfileInfo(@PathVariable String id,
                                                   @RequestHeader("user-id") String userId) {
        ProfileInfoDto profileInfo = new ProfileInfoDto(id, "Nguyen Van A", Collections.singletonList("<EMAIL>"), Collections.singletonList("0931111111"),"1 street", "false", Constants.GENDER_MALE, "engineer", new Timestamp(System.currentTimeMillis()), new Timestamp(System.currentTimeMillis()));
        return ResponseFactory.getSuccessResponse("Mock api", profileInfo);
    }
    /**
     * API delete profile for customer
     * @param id customer id
     * @return response after deleting profile
     */
    @DeleteMapping("/profile/{id}")
    public Response deleteProfile(@PathVariable String id,
                                  @RequestHeader("user-id") String userId) {
        return ResponseFactory.getSuccessResponse("Mock api", null);
    }

    /**
     * API get profile score for customer
     * @param id customer id
     * @return response with profile score
     */
    @GetMapping("/profile/{id}/scores")
    public Response<ProfileScoreDto> getProfileScore(@PathVariable String id,
                                                     @RequestHeader("user-id") String userId) {
        ProfileScoreDto dto = ProfileScoreDto.builder()
                .profileId(String.valueOf(id))
                .leadScore(6.12)
                .clvScore(1L)
                .dataQualityScore(0.3)
                .engagementScore(0.2)
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();
        return ResponseFactory.getSuccessResponse("Mock api", dto);
    }

    /**
     * API get predictive score for customer
     * @param id customer id
     * @return response with predictive score
     */
    @GetMapping("/profile/{id}/predictive-scores")
    public Response<PredictionScoreDto> getPredictiveScore(@PathVariable String id,
                                                           @RequestHeader("user-id") String userId) {
        PredictionScoreDto predictionScoreDto = PredictionScoreDto.builder()
                .profileId(String.valueOf(id))
                .highLtv(6.12)
                .recommends(1.2)
                .purchaseFrequently(1.0)
                .convenienceShopper(1.0)
                .churn(1.0)
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();
        return ResponseFactory.getSuccessResponse("Mock api", predictionScoreDto);
    }


    /**
     * API get audience tags for customer
     * @param id customer id
     * @return response with audience tags
     */
    @GetMapping("/profile/{id}/audience-tags")
    public Response<List<AudienceTagDto>> getAudienceTags(@PathVariable String id,
                                                          @RequestHeader("user-id") String userId) {
        List<AudienceTagDto> audienceTags = new ArrayList<>();
        AudienceTagDto audienceTagDto = AudienceTagDto.builder()
                .profileId(String.valueOf(id))
                .audienceId("1")
                .audienceStudio("tag1")
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();
        audienceTags.add(audienceTagDto);

        return ResponseFactory.getSuccessResponse("Mock api", audienceTags);
    }

    /**
     * API get sources for customer
     * @param id customer id
     * @return response with sources
     */
    @GetMapping("/profile/{id}/sources")
    public Response<List<CustomerSourceDto>> getSources(@PathVariable String id,
                                                        @RequestHeader("user-id") String userId) {
        List<CustomerSourceDto> customerSourceDtos = new ArrayList<>();
        CustomerSourceDto customerSourceDto = CustomerSourceDto.builder()
                .customerId(String.valueOf(id))
                .sourceId("1")
                .sourceName("source1")
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();
        customerSourceDtos.add(customerSourceDto);
        return ResponseFactory.getSuccessResponse("Mock api", customerSourceDtos);
    }

    /**
     * API to get purchase details of a customer.
     * @param id customer id
     * @return response with purchase details
     */
    @GetMapping("/profile/{id}/purchases")
    public Response getPurchases(@PathVariable String id,
                                 @RequestHeader("user-id") String userId) {
        return null;
    }
    /**
     * Retrieves the purchase statistics of a customer.
     *
     * @param id the ID of the customer whose purchase statistics are to be retrieved
     * @return response containing the customer's purchase statistics
     */
    @GetMapping("/profile/{id}/purchases/statistics")
    public Response<CustomerPurchaseStatisticDto> getPurchasesStatistics(@PathVariable String id,
                                                                         @RequestHeader("user-id") String userId) {
        return ResponseFactory.getSuccessResponse("Mock api", new CustomerPurchaseStatisticDto(id,56, 2_459_260.46, 135_593_186.48));
    }
    /**
     * API to get connections of a customer.
     * @param id customer id
     * @return response with connections
     */
    @GetMapping("/profile/{id}/connections")
    public Response<ConnectionDto> getConnections(@PathVariable String id,
                                                  @RequestHeader("user-id") String userId) {
        Map<ConnectionDto.ChannelConnectType, List<String>> channels = new HashMap<>();
        List<String> emailList = new ArrayList<>();
        emailList.add("<EMAIL>");
        emailList.add("<EMAIL>");
        channels.put(ConnectionDto.ChannelConnectType.EMAIL, emailList);

        List<String> phoneList = new ArrayList<>();
        phoneList.add("0123456789");
        channels.put(ConnectionDto.ChannelConnectType.PHONE, phoneList);
        ConnectionDto connectionDto = ConnectionDto.builder()
                .profileId(id)
                .channels(channels)
                .build();

        return ResponseFactory.getSuccessResponse("Mock api", connectionDto);
    }

    /**
     * Retrieves the channel frequencies of a customer.
     * @param id the ID of the customer whose channel frequencies are to be retrieved
     * @return response containing the customer's channel frequencies
     */
    @GetMapping("/profile/{id}/channels")
    public Response<ChannelFreqDto> getChannels(@PathVariable String id,
                                                @RequestHeader("user-id") String userId) {
        List<ChannelFreqDto.ChannelFreqInfo> channels = new ArrayList<>();
        channels.add(ChannelFreqDto.ChannelFreqInfo.builder().channel(ChannelFreqDto.ChannelConnectType.EMAIL).freq(1).build());
        channels.add(ChannelFreqDto.ChannelFreqInfo.builder().channel(ChannelFreqDto.ChannelConnectType.WEBSITE).freq(30).build());
        channels.add(ChannelFreqDto.ChannelFreqInfo.builder().channel(ChannelFreqDto.ChannelConnectType.MOBILE).freq(12).build());
        ChannelFreqDto channelFreqDto = ChannelFreqDto.builder()
                .profileId(id)
                .channelFreqInfos(channels)
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();
        return ResponseFactory.getSuccessResponse("Mock api", channelFreqDto);
    }

    /**
     * API to retrieve email metrics for a customer.
     * @param id the ID of the customer whose email metrics are to be retrieved
     * @return response containing the customer's email metrics
     */
    @GetMapping("/profile/{id}/email-metrics")
    public Response<EmailMetricsDto> getEmailMetrics(@PathVariable String id,
                                                     @RequestHeader("user-id") String userId) {
        EmailMetricsDto emailMetricsDto = EmailMetricsDto.builder()
                .profileId(id)
                .delivered(2)
                .openRate(0.5)
                .clickRate(0.6)
                .conversionRate(0.7)
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();

        return ResponseFactory.getSuccessResponse("Mock api", emailMetricsDto);
    }
    /**
     * API get devices for customer
     * @param id customer id
     * @return response with devices
     */
    @GetMapping("/profile/{id}/devices")
    public Response<DeviceUsageDto> getDevices(@PathVariable String id,
                                               @RequestHeader("user-id") String userId) {
        List<DeviceUsageDto.DeviceUsageInfo> deviceUsageInfos = new ArrayList<>();

        deviceUsageInfos.add(DeviceUsageDto.DeviceUsageInfo.builder()
                .deviceType(DeviceUsageDto.DeviceType.MOBILE)
                .lastActiveAt(new Timestamp(System.currentTimeMillis())).build());
        deviceUsageInfos.add(DeviceUsageDto.DeviceUsageInfo.builder()
                .deviceType(DeviceUsageDto.DeviceType.DESKTOP)
                .lastActiveAt(new Timestamp(System.currentTimeMillis())).build());

        DeviceUsageDto deviceUsageDto = DeviceUsageDto.builder()
                .profileId(id)
                .deviceUsages(deviceUsageInfos)
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();
        return ResponseFactory.getSuccessResponse("Mock api", deviceUsageDto);
    }

    /**
     * API get search history of customer
     * @param id customer id
     * @return response with search history
     */
    @GetMapping("/profile/{id}/search-history")
    public Response<SearchHistoryDto> getSearchHistory(@PathVariable String id,
                                                       @RequestHeader("user-id") String userId) {
        List<KeywordFreq> keywordFreqList = Arrays.asList(
                new KeywordFreq("cdp bizfly", 5),
                new KeywordFreq("shopping", 3)
        );

        SearchHistoryDto dto = SearchHistoryDto.builder()
                .profileId(id)
                .keywordFreq(keywordFreqList)
                .build();
        return ResponseFactory.getSuccessResponse("Mock api", dto);
    }

    /**
     * API get preferences of customer
     * @param id customer id
     * @return response with preferences
     */
    @GetMapping("/profile/{id}/preferences")
    public Response<PreferenceDto> getPreferences(@PathVariable String id,
                                                  @RequestHeader("user-id") String userId) {
        Map<String, Object> preferences = Map.of(
                "gender", Constants.GENDER_MALE,
                "age", 20
        );
        return ResponseFactory.getSuccessResponse("Mock api", PreferenceDto.builder()
                .profileId(id)
                .preferences(preferences));
    }


}
